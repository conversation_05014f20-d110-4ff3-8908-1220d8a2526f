import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';

class ConnectivityHelper {
  static final Connectivity _connectivity = Connectivity();

  /// Checks if device has internet connectivity
  /// Returns true if connected, false if no connection
  static Future<bool> hasConnection() async {
    try {
      final List<ConnectivityResult> connectivityResult = 
          await _connectivity.checkConnectivity();
      
      // Check if any connection type is available
      final bool hasAnyConnection = connectivityResult.any((result) => 
          result == ConnectivityResult.mobile ||
          result == ConnectivityResult.wifi ||
          result == ConnectivityResult.ethernet ||
          result == ConnectivityResult.vpn);
      
      debugPrint('🌐 Connectivity check: $hasAnyConnection (${connectivityResult.join(', ')})');
      return hasAnyConnection;
    } catch (e) {
      debugPrint('❌ Connectivity check failed: $e');
      // If connectivity check fails, assume connection exists to avoid blocking user
      return true;
    }
  }

  /// Gets a user-friendly error message for no connection
  static String getNoConnectionMessage() {
    return 'No internet connection. Please check your network settings and try again.';
  }

  /// Gets a user-friendly error message for connection issues
  static String getConnectionErrorMessage() {
    return 'Connection error. Please check your internet connection and try again.';
  }

  /// Checks connection and throws exception with user-friendly message if no connection
  static Future<void> ensureConnection() async {
    final hasConn = await hasConnection();
    if (!hasConn) {
      throw Exception(getNoConnectionMessage());
    }
  }
}
