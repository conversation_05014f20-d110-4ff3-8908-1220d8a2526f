                        -HC:\dev\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\29.0.13113456
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\29.0.13113456
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\29.0.13113456\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\flutterprojects\atria\build\app\intermediates\cxx\Debug\4im5o4p3\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\flutterprojects\atria\build\app\intermediates\cxx\Debug\4im5o4p3\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BC:\flutterprojects\atria\android\app\.cxx\Debug\4im5o4p3\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2