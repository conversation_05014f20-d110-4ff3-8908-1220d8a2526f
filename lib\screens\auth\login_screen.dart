import 'dart:math';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';
import 'package:supabase_auth_ui_custom/supabase_auth_ui_custom.dart';
import 'package:url_launcher/url_launcher.dart'; // Import url_launcher
import '../../services/supabase_service.dart';
import '../../services/user_profile_service.dart';
import '../../constants/font_sizes.dart';

import '../home_page.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  bool _isNavigating = false;
  late AnimationController _pulseController;
  late List<AnimationController> _floatingControllers;

  @override
  void initState() {
    super.initState();
    // Sessions are already cleared in main.dart on app startup
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat(reverse: true);

    _floatingControllers = List.generate(9, (index) {
      final controller = AnimationController(
        vsync: this,
        duration: Duration(milliseconds: 3000 + (index * 400)),
      );
      controller.repeat();
      return controller;
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    for (final controller in _floatingControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<String> _getDoctorName() async {
    try {
      final userProfile = await UserProfileService.getUserProfile();
      if (userProfile.doctorNameEn.isNotEmpty) {
        return userProfile.doctorNameEn;
      }
    } catch (e) {
      debugPrint('Error getting doctor name: $e');
    }
    return 'Doctor';
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  primaryColor,
                  primaryColor.withAlpha(230),
                  const Color(0xFF1565C0),
                  const Color(0xFF0D47A1),
                ],
                stops: const [0.0, 0.3, 0.7, 1.0],
              ),
            ),
          ),

          ...List.generate(
            9,
            (index) => _buildFloatingCircle(
              index,
              screenWidth,
              screenHeight,
              primaryColor,
            ),
          ),

          SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: screenHeight * 0.08),

                    _buildAppBranding(primaryColor),

                    SizedBox(height: screenHeight * 0.03),

                    _buildWelcomeSection(theme),

                    SizedBox(height: screenHeight * 0.04),

                    // Email authentication
                    _buildEmailAuthForm(),

                    SizedBox(height: screenHeight * 0.04),

                    _buildAppDescription(),

                    SizedBox(height: screenHeight * 0.03),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingCircle(
    int index,
    double screenWidth,
    double screenHeight,
    Color primaryColor,
  ) {
    final size = 50.0 + (index * 15);

    double baseLeft, baseTop;

    if (index < 6) {
      baseLeft = (index * screenWidth / 6) + (index * 25);
      baseTop = (index * screenHeight / 8) + (index * 40);
    } else {
      switch (index) {
        case 6:
          baseLeft = screenWidth * 0.8;
          baseTop = screenHeight * 0.15;
          break;
        case 7:
          baseLeft = screenWidth * 0.1;
          baseTop = screenHeight * 0.45;
          break;
        case 8:
          baseLeft = screenWidth * 0.5;
          baseTop = screenHeight * 0.75;
          break;
        default:
          baseLeft = screenWidth * 0.5;
          baseTop = screenHeight * 0.5;
      }
    }

    return AnimatedBuilder(
      animation: _floatingControllers[index],
      builder: (context, child) {
        final animationValue = _floatingControllers[index].value;

        final horizontalOffset =
            40 *
            (index % 2 == 0 ? 1 : -1) *
            (0.5 + 0.5 * sin(animationValue * 2 * pi));
        final verticalOffset =
            30 *
            (index % 3 == 0 ? 1 : -1) *
            (0.5 + 0.5 * sin((animationValue * 2 * pi) + (index * 0.5)));

        final rotation = (animationValue * 2 * pi * 0.1) + (index * 0.3);

        return Positioned(
          left: baseLeft + horizontalOffset,
          top: baseTop + verticalOffset,
          child: Transform.rotate(
            angle: rotation,
            child: Opacity(
              opacity:
                  0.08 +
                  (index * 0.03) +
                  (0.02 * sin(animationValue * 2 * pi).abs()),
              child: Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withAlpha(25),
                  border: Border.all(
                    color: Colors.white.withAlpha(40),
                    width: 1,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAppBranding(Color primaryColor) {
    return Column(
      children: [
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(30),
                blurRadius: 20,
                spreadRadius: 5,
              ),
              BoxShadow(
                color: primaryColor.withAlpha(100),
                blurRadius: 40,
                spreadRadius: 10,
              ),
            ],
          ),
          child: Center(
            child: AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                return Stack(
                  alignment: Alignment.center,
                  children: [
                    AnimatedOpacity(
                      opacity: 0.2 + (_pulseController.value * 0.3),
                      duration: const Duration(milliseconds: 100),
                      child: Transform.scale(
                        scale: 0.7 + (_pulseController.value * 0.4),
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: primaryColor.withAlpha(50),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),

                    AnimatedOpacity(
                      opacity: 0.4 + (_pulseController.value * 0.2),
                      duration: const Duration(milliseconds: 100),
                      child: Transform.scale(
                        scale: 0.8 + (_pulseController.value * 0.2),
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: primaryColor.withAlpha(30),
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),

                    Transform.scale(
                      scale: 0.9 + (_pulseController.value * 0.1),
                      child: Image.asset(
                        'lib/assets/images/heartmain.png',
                        width: 100,
                        height: 100,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),

        const SizedBox(height: 24),

        Container(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(20),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(color: Colors.white.withAlpha(50), width: 1),
          ),
          child: Text(
            'ATRIA',
            style: TextStyle(
              color: Colors.white,
              fontSize: FontSizes.heading1 + 8,
              fontWeight: FontWeight.bold,
              letterSpacing: 3,
              shadows: [
                Shadow(
                  offset: const Offset(2, 2),
                  blurRadius: 4.0,
                  color: Colors.black.withAlpha(50),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 8),

        Text(
          'Your Echo Partner',
          style: TextStyle(
            color: Colors.white.withAlpha(200),
            fontSize: FontSizes.bodyLarge,
            fontWeight: FontWeight.w500,
            letterSpacing: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildWelcomeSection(ThemeData theme) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(15),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withAlpha(30), width: 1),
          ),
          child: Column(
            children: [
              Text(
                'Welcome',
                style: theme.textTheme.headlineLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: FontSizes.heading1 + 12,
                  shadows: [
                    Shadow(
                      offset: const Offset(1, 1),
                      blurRadius: 3.0,
                      color: Colors.black.withAlpha(77),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              FutureBuilder<String>(
                future: _getDoctorName(),
                builder: (context, snapshot) {
                  final doctorName = snapshot.data ?? 'Doctor';
                  return Text(
                    doctorName,
                    style: theme.textTheme.headlineMedium?.copyWith(
                      color: Colors.white.withAlpha(220),
                      fontWeight: FontWeight.bold,
                      fontSize: FontSizes.heading1 + 4,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _handleAuthSuccess(Session response) async {
    if (_isNavigating) return;
    setState(() {
      _isNavigating = true;
    });

    try {
      // Wait for Supabase session to stabilize
      debugPrint('⏳ Waiting for Supabase session stabilization...');
      await Future.delayed(const Duration(milliseconds: 500));

      // Verify account exists in Supabase
      debugPrint('🔍 Verifying account exists in Supabase...');
      final currentUser = SupabaseService.currentUser;
      final isAuthenticated = currentUser != null;
      debugPrint('🔍 Current user: ${currentUser?.email}');
      debugPrint('🔍 Supabase authentication status: $isAuthenticated');

      if (!isAuthenticated) {
        debugPrint('❌ Account not found in Supabase');
        throw Exception('Supabase authentication failed');
      }

      if (!mounted) return;

      // Navigate directly to HomePage after successful authentication
      debugPrint('✅ Supabase Verified → HomePage');
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const HomePage()),
      );
    } catch (e) {
      debugPrint('❌ Error during sign-in process: $e');
      setState(() {
        _isNavigating = false;
      });
      if (mounted) {
        _showAuthError(e);
      }
    }
  }

  void _showAuthError(dynamic error) {
    String errorMessage = 'Sign-in failed. Please try again.';
    if (error.toString().contains('No internet connection')) {
      errorMessage =
          'No internet connection. Please check your network settings and try again.';
    } else if (error.toString().contains('Supabase authentication failed')) {
      errorMessage =
          'Account verification failed. Please try signing in again.';
    } else if (error.toString().contains('network') ||
        error.toString().contains('connection')) {
      errorMessage =
          'Network error. Please check your connection and try again.';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(errorMessage),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () {
            // User can try signing in again
          },
        ),
      ),
    );
  }

  Widget _buildEmailAuthForm() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withAlpha(30), width: 1),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          // Override input decoration theme for white text
          inputDecorationTheme: InputDecorationTheme(
            labelStyle: const TextStyle(color: Colors.white70),
            hintStyle: const TextStyle(color: Colors.white60),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.white.withAlpha(100)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.white, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red, width: 2),
            ),
            filled: true,
            fillColor: Colors.white.withAlpha(20),
          ),
          // Override text theme for white text
          textTheme: Theme.of(context).textTheme.copyWith(
            bodyLarge: const TextStyle(color: Colors.white),
            bodyMedium: const TextStyle(color: Colors.white),
            bodySmall: const TextStyle(color: Colors.white70),
            labelLarge: const TextStyle(color: Colors.white),
            labelMedium: const TextStyle(color: Colors.white70),
            labelSmall: const TextStyle(color: Colors.white60),
          ),
          // Override button theme for white text
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.white,
              foregroundColor: Colors.black,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(foregroundColor: Colors.white),
          ),
        ),
        child: Column(
          children: [
            SupaEmailAuthCustom(
              onSignInComplete:
                  (response) => _handleAuthSuccess(response.session!),
              onError: (error) {
                _showAuthError(error);
              },
              prefixIconEmail: const Icon(Icons.email, color: Colors.white70),
              prefixIconPassword: const Icon(Icons.lock, color: Colors.white70),
            ),
            const SizedBox(height: 20),
            Text(
              'If you need help, contact us:',
              style: TextStyle(
                color: Colors.white.withAlpha(200),
                fontSize: FontSizes.bodyLarge,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            _buildContactRow(
              icon: Icons.email,
              label: '<EMAIL>',
              onTap: () async {
                final Uri emailLaunchUri = Uri(
                  scheme: 'mailto',
                  path: '<EMAIL>',
                );
                if (await canLaunchUrl(emailLaunchUri)) {
                  await launchUrl(emailLaunchUri);
                } else {
                  // Handle error
                }
              },
            ),
            const SizedBox(height: 8),
            _buildContactRow(
              icon: FontAwesomeIcons.whatsapp,
              label: '+20 ************',
              onTap: () async {
                final Uri whatsappLaunchUri = Uri.parse(
                  'whatsapp://send?phone=+201211644172',
                );
                if (await canLaunchUrl(whatsappLaunchUri)) {
                  await launchUrl(whatsappLaunchUri);
                } else {
                  // Handle error
                }
              },
            ),
            const SizedBox(height: 8),
            _buildContactRow(
              icon: FontAwesomeIcons.facebook,
              label: 'Facebook Page',
              onTap: () async {
                final Uri facebookLaunchUri = Uri.parse(
                  'https://www.facebook.com/profile.php?id=61577427464079',
                );
                if (await canLaunchUrl(facebookLaunchUri)) {
                  await launchUrl(facebookLaunchUri);
                } else {
                  // Handle error
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppDescription() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(10),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withAlpha(20), width: 1),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildFeatureIcon(FontAwesomeIcons.bolt, 'Quick Reports'),
              _buildFeatureIcon(FontAwesomeIcons.circleCheck, 'Efficient'),
              _buildFeatureIcon(FontAwesomeIcons.cubes, 'Fully Modular'),
              _buildFeatureIcon(FontAwesomeIcons.clock, 'Time saver'),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Your best echo reporting partner',
            style: TextStyle(
              color: Colors.white.withAlpha(180),
              fontSize: FontSizes.bodyMedium,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureIcon(IconData icon, String label) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withAlpha(20),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white.withAlpha(40), width: 1),
          ),
          child: FaIcon(icon, color: Colors.white, size: 20),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withAlpha(160),
            fontSize: FontSizes.footnote,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildContactRow({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(icon, color: Colors.white.withAlpha(200), size: 20),
            const SizedBox(width: 12),
            Flexible(
              child: Text(
                label,
                style: TextStyle(
                  color: Colors.white.withAlpha(200),
                  fontSize: FontSizes.bodyMedium,
                  fontWeight: FontWeight.w500,
                  decoration: TextDecoration.underline,
                  decorationColor: Colors.white.withAlpha(150),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
