-- Create the subscriptions table
CREATE TABLE IF NOT EXISTS public.subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id TEXT,
  purchase_token TEXT,
  status TEXT NOT NULL DEFAULT 'inactive',
  expiry_date TIMESTAMP WITH TIME ZONE,
  is_in_grace_period BOOLEAN DEFAULT FALSE,
  is_in_trial_period BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add RLS policies
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to read only their own subscriptions
CREATE POLICY "Users can view their own subscriptions"
  ON public.subscriptions
  FOR SELECT
  USING (auth.uid() = user_id);

-- Policy to allow users to insert their own subscriptions
CREATE POLICY "Users can insert their own subscriptions"
  ON public.subscriptions
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Policy to allow users to update their own subscriptions
CREATE POLICY "Users can update their own subscriptions"
  ON public.subscriptions
  FOR UPDATE
  USING (auth.uid() = user_id);

-- Create function to create the subscriptions table
CREATE OR REPLACE FUNCTION public.create_subscriptions_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Create the subscriptions table if it doesn't exist
  CREATE TABLE IF NOT EXISTS public.subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    product_id TEXT,
    purchase_token TEXT,
    status TEXT NOT NULL DEFAULT 'inactive',
    expiry_date TIMESTAMP WITH TIME ZONE,
    is_in_grace_period BOOLEAN DEFAULT FALSE,
    is_in_trial_period BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  );

  -- Add RLS policies if they don't exist
  IF NOT EXISTS (
    SELECT 1 FROM pg_policies 
    WHERE tablename = 'subscriptions' 
    AND policyname = 'Users can view their own subscriptions'
  ) THEN
    -- Enable RLS
    ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

    -- Policy to allow users to read only their own subscriptions
    CREATE POLICY "Users can view their own subscriptions"
      ON public.subscriptions
      FOR SELECT
      USING (auth.uid() = user_id);

    -- Policy to allow users to insert their own subscriptions
    CREATE POLICY "Users can insert their own subscriptions"
      ON public.subscriptions
      FOR INSERT
      WITH CHECK (auth.uid() = user_id);

    -- Policy to allow users to update their own subscriptions
    CREATE POLICY "Users can update their own subscriptions"
      ON public.subscriptions
      FOR UPDATE
      USING (auth.uid() = user_id);
  END IF;
END;
$$;
