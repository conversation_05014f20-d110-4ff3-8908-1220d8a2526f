enum Gender { male, female }

enum ValveStructuralAbnormality {
  normal,
  bicuspid,
  calcified,
  myxomatous,
  rheumatic,
  thrombus,
  vegetation,
  prosthetic,
  other,
}

enum RegurgitationSeverity {
  none,
  trace,
  mild,
  moderate,
  moderateSevere,
  severe,
}

enum StenosisSeverity { none, mild, moderate, severe, critical }

enum DiastolicDysfunctionGrade { normal, grade1, grade2, grade3, grade4 }

enum WallMotionScore { normal, hypokinetic, akinetic, dyskinetic, aneurysmal }

enum PericardialEffusionSeverity { none, small, moderate, severe, tamponade }

enum PericardialEffusionLocation {
  none,
  circumferential,
  anterior,
  posterior,
  lateral,
  apical,
  loculated,
}

enum IVCCollapsibility { normal, reduced, absent }

enum SeptalDeformity {
  none,
  hypertrophy,
  flattening,
  bowing,
  aneurysm,
  vsd,
  rightToLeftShunt,
}

enum UnusualFinding {
  none,
  mass,
  thrombus,
  vegetation,
  rupture,
  pseudoaneurysm,
  dissection,
  flap,
  intimalTear,
  apicalBallooning,
  takotsubo,
  cardiacTamponade,
  restrictivePhysiology,
  constrictivePericarditis,
  hypertrophicObstruction,
  cardiacAmyloidosis,
  cardiacSarcoidosis,
  cardiacTumor,
  cardiacMetastasis,
  pulmonaryEmbolism,
  infectiveEndocarditis,
  other,
}

enum ProstheticValveType { mechanical, bioprosthetic }

enum ProstheticValveFunction {
  normal,

  svdLeafletCalcification,
  svdLeafletTear,
  svdStentFracture,

  ppm,
  paravalvularLeak,
  pannus,
  malposition,

  endocarditis,

  thrombus,
}

enum LVSegment {
  basalAnterior,
  basalAnteroseptal,
  basalInferoseptal,
  basalInferior,
  basalInferolateral,
  basalAnterolateral,
  midAnterior,
  midAnteroseptal,
  midInferoseptal,
  midInferior,
  midInferolateral,
  midAnterolateral,
  apicalAnterior,
  apicalSeptal,
  apicalInferior,
  apicalLateral,
  apex,
}

enum Priority { routine, urgent, critical, followUp, requested }

enum Location { inpatient, outpatient, emergency, icu, ccu, other }

enum WallMotionAbnormality { no, yes }

enum WallMotionAbnormalityType { segmental, global }

enum RVSize { normal, dilated }

enum RASize { normal, dilated }

enum CardiacRhythm {
  sinusRhythm,
  atrialFibrillation,
  atrialFlutter,
  ectopicAtrialRhythm,
  junctionalRhythm,
  ventricularRhythm,
  heartBlock,
  pacedRhythm,
  other,
}

extension LVSegmentExtension on LVSegment {
  String get displayName {
    switch (this) {
      case LVSegment.basalAnterior:
        return 'Basal Anterior';
      case LVSegment.basalAnteroseptal:
        return 'Basal Anteroseptal';
      case LVSegment.basalInferoseptal:
        return 'Basal Inferoseptal';
      case LVSegment.basalInferior:
        return 'Basal Inferior';
      case LVSegment.basalInferolateral:
        return 'Basal Inferolateral';
      case LVSegment.basalAnterolateral:
        return 'Basal Anterolateral';
      case LVSegment.midAnterior:
        return 'Mid Anterior';
      case LVSegment.midAnteroseptal:
        return 'Mid Anteroseptal';
      case LVSegment.midInferoseptal:
        return 'Mid Inferoseptal';
      case LVSegment.midInferior:
        return 'Mid Inferior';
      case LVSegment.midInferolateral:
        return 'Mid Inferolateral';
      case LVSegment.midAnterolateral:
        return 'Mid Anterolateral';
      case LVSegment.apicalAnterior:
        return 'Apical Anterior';
      case LVSegment.apicalSeptal:
        return 'Apical Septal';
      case LVSegment.apicalInferior:
        return 'Apical Inferior';
      case LVSegment.apicalLateral:
        return 'Apical Lateral';
      case LVSegment.apex:
        return 'Apex';
    }
  }
}
