plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

import java.util.Properties
import java.io.FileInputStream
import java.io.File

// Load keystore properties for upload certificate signing
val keystorePropertiesFile = rootProject.file("key.properties")
val keystoreProperties = Properties()
val useUploadSigning = if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(FileInputStream(keystorePropertiesFile))
    true
} else {
    println("key.properties file not found. Using debug signing configuration.")
    false
}

android {
    namespace = "com.syvursoft.atria"
    compileSdk = 35
    ndkVersion = "29.0.13113456"



    compileOptions {
        // Enable core library desugaring
        isCoreLibraryDesugaringEnabled = true
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    signingConfigs {
        if (useUploadSigning) {
            create("upload") {
                keyAlias = keystoreProperties["keyAlias"] as String
                keyPassword = keystoreProperties["keyPassword"] as String
                storeFile = rootProject.file(keystoreProperties["storeFile"] as String)
                storePassword = keystoreProperties["storePassword"] as String
            }
        }
        getByName("debug") {
            // Use debug keystore for development
        }
    }


    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.syvursoft.atria"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24
        targetSdk = 35
        versionCode = 9
        versionName = "9.0.0"

        // Support for 16KB page sizes (Android 15+)
        ndk {
            abiFilters += listOf("arm64-v8a", "armeabi-v7a", "x86_64")
        }
    }

    buildTypes {
        release {
            // Use upload signing for Google Play Console uploads
            // Google Play will re-sign with app signing key for distribution
            signingConfig = if (useUploadSigning) {
                signingConfigs.getByName("upload")
            } else {
                signingConfigs.getByName("debug")
            }

            // Enable R8 code shrinking for release builds
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "src/main/res/raw/keep_rules.txt"
            )
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // ... existing dependencies

    // Update to version 2.1.4 which is the latest available that meets the requirement
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.1.4")

    implementation ("com.google.android.play:integrity:1.4.0")


    // Play Asset Delivery Library
    implementation("com.google.android.play:asset-delivery:2.1.0")

    // Play Feature Delivery Library
    implementation("com.google.android.play:feature-delivery:2.1.0")

    // Play In-App Reviews Library
    implementation("com.google.android.play:review:2.0.1")

    // Play In-App Updates Library
    implementation("com.google.android.play:app-update:2.1.0")
    implementation("com.google.android.play:app-update-ktx:2.1.0")


}
