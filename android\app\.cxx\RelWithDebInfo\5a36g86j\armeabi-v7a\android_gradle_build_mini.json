{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterprojs\\atria\\android\\app\\.cxx\\RelWithDebInfo\\5a36g86j\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterprojs\\atria\\android\\app\\.cxx\\RelWithDebInfo\\5a36g86j\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}