class SmartTemplate {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final DateTime lastUsed;
  final Map<String, dynamic> templateData;

  SmartTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.lastUsed,
    required this.templateData,
  });

  factory SmartTemplate.fromJson(Map<String, dynamic> json) {
    return SmartTemplate(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      createdAt: DateTime.parse(json['createdAt']),
      lastUsed: DateTime.parse(json['lastUsed']),
      templateData: Map<String, dynamic>.from(json['templateData']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'templateData': templateData,
    };
  }

  SmartTemplate copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? lastUsed,
    Map<String, dynamic>? templateData,
  }) {
    return SmartTemplate(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
      templateData: templateData ?? this.templateData,
    );
  }
}
