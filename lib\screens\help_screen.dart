import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../constants/font_sizes.dart';

class HelpScreen extends StatelessWidget {
  const HelpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildHelpCard(
            context,
            title: 'How to Use ATRIA',
            description:
                'Learn the basics of creating and managing echo reports',
            icon: FontAwesomeIcons.circleQuestion,
            onTap: () {
              _showHelpDialog(
                context,
                'How to Use ATRIA',
                'ATRIA is designed to help you create and manage echocardiogram reports easily.\n'
                    '1. Create a new report from the home screen or app menu\n'
                    '2. Fill in patient information and echo parameters\n'
                    '3. Preview the generated report\n'
                    '4. Save, print, or share the report as needed\n'
                    '5. Access saved reports from the My Reports section',
              );
            },
          ),

          _buildHelpCard(
            context,
            title: 'Setting Up Your Profile',
            description: 'Customize your name, language, logo, and background',
            icon: FontAwesomeIcons.userGear,
            onTap: () {
              _showHelpDialog(
                context,
                'Setting Up Your Profile',
                'To create a professional profile in ATRIA:\n'
                    '1. Access your profile by tapping "Profile" in the app menu\n'
                    '2. PDF Header Customization:\n'
                    '   • This section shows a preview of how your header will appear in reports\n'
                    '   • Changes to doctor information will be reflected in this preview\n'
                    '3. Doctor Information (English):\n'
                    '   • Enter your name, credentials, and location/department in English\n'
                    '   • This information appears on the left side of your report header\n'
                    '   • Example format: "Dr. John Smith" (name), "MD, FACC" (credentials), "Cardiology Dept." (location)\n'
                    '4. Language Options:\n'
                    '   • Toggle "Use Arabic in Right Section" to switch between Arabic and English\n'
                    '   • When enabled, you can enter your information in Arabic\n'
                    '   • When disabled, you can enter custom text for the right header\n'
                    '5. Doctor Information (Arabic/Right Header):\n'
                    '   • Enter your name, credentials, and location in Arabic when toggle is enabled\n'
                    '   • Or enter custom text for each line when toggle is disabled\n'
                    '   • This information appears on the right side of your report header\n'
                    '6. Logo Selection:\n'
                    '   • Tap on "Custom Logo" to select a logo image from your device\n'
                    '   • Recommended size: 800x800 pixels (square format)\n'
                    '   • Supported formats: PNG, JPG/JPEG\n'
                    '   • Transparent PNG logos work best for professional appearance\n'
                    '   • The logo will appear in the center of your PDF report header\n'
                    '   • To remove a logo, tap the delete icon next to the preview\n'
                    '7. Background Images:\n'
                    '   • Main Background: Appears behind all content with 70% transparency\n'
                    '   • Positioned Backgrounds: Place images at specific corners (top-left, top-right, etc.)\n'
                    '   • Section Backgrounds: Add images to specific sections (header, patient data, etc.)\n'
                    '   • Recommended size: 1200x1200 pixels for main background\n'
                    '   • Supported formats: PNG, JPG/JPEG\n'
                    '   • To remove any background, tap the delete icon next to its preview\n'
                    '8. Text Color Customization:\n'
                    '   • Doctor Title Color: Changes the color of doctor name and credentials at the top\n'
                    '   • Report Title Color: Changes the color of the "Echocardiogram Report" title\n'
                    '   • Patient Info Text Color: Changes text color in the patient information section\n'
                    '   • Echo Parameters Text Color: Changes text color in the measurements section\n'
                    '   • Interpretation Text Color: Changes text color in the findings section\n'
                    '   • Footer Text Color: Changes text color in the report footer\n'
                    '   • For each color, you can use the color picker or enter specific HEX/RGB values\n'
                    '9. Save your profile by tapping the "Save Profile" button at the bottom\n'
                    'Tips for a Professional Profile:\n'
                    '• Use high-resolution images with transparent backgrounds for logos\n'
                    '• Choose subtle, light colors for backgrounds to avoid interfering with text\n'
                    '• Select complementary colors for text that maintain good readability\n'
                    '• Preview your reports after making changes to ensure everything looks professional\n'
                    '• Your profile settings will be applied to all new reports you create',
              );
            },
          ),

          _buildHelpCard(
            context,
            title: 'Creating Reports',
            description: 'Step-by-step guide to creating echo reports',
            icon: FontAwesomeIcons.fileCirclePlus,
            onTap: () {
              _showHelpDialog(
                context,
                'Creating Reports',
                'To create a new echo report:\n'
                    '1. Tap "Quick Report" on the home screen or "New Report" in the menu\n'
                    '2. Enter patient information (name, age, gender, etc.)\n'
                    '3. Fill in the echo parameters in each section\n'
                    '4. For wall motion abnormalities, select segments on the heart diagram\n'
                    '5. Tap "Generate Report" to create the PDF\n'
                    '6. Preview the report and save it if needed',
              );
            },
          ),

          _buildHelpCard(
            context,
            title: 'Managing Reports',
            description: 'How to view, edit, and delete saved reports',
            icon: FontAwesomeIcons.folderOpen,
            onTap: () {
              _showHelpDialog(
                context,
                'Managing Reports',
                'To manage your saved reports:\n'
                    '1. Go to "My Reports" from the home screen or app menu\n'
                    '2. View the list of all saved reports\n'
                    '3. Tap on a report to open and view it\n'
                    '4. Use the print or share buttons to export the report\n'
                    '5. To delete a report, tap the delete icon and confirm',
              );
            },
          ),

          _buildHelpCard(
            context,
            title: 'Troubleshooting',
            description: 'Solutions to common issues',
            icon: FontAwesomeIcons.screwdriverWrench,
            onTap: () {
              _showHelpDialog(
                context,
                'Troubleshooting',
                'Common issues and solutions:\n'
                    '• If the app is slow, try restarting it\n'
                    '• If reports fail to save, check your device storage\n'
                    '• For printing issues, ensure your printer is properly connected\n'
                    '• If the PDF preview doesn\'t load, try generating the report again\n'
                    '• For persistent issues, contact support',
              );
            },
          ),

          _buildHelpCard(
            context,
            title: 'Contact Support',
            description: 'Get help from our support team',
            icon: FontAwesomeIcons.headset,
            onTap: () {
              _showContactSupportDialog(context);
            },
          ),

          const SizedBox(height: 24),

          Center(
            child: Text(
              'ATRIA v9.0.0',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: FontSizes.bodyLarge,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHelpCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withAlpha((0.1 * 255).round()),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: FaIcon(
                  icon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: FontSizes.heading2,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: FontSizes.bodyLarge,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  void _showHelpDialog(BuildContext context, String title, String content) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: 16,
            backgroundColor: Colors.transparent,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.98,
                maxWidth: MediaQuery.of(context).size.width * 0.99,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.white, Colors.blue.shade50],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withAlpha(76),
                    blurRadius: 4,
                    spreadRadius: 2,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 4,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with gradient background
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF1E88E5),
                          const Color(0xFF1976D2),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(51),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.help_outline,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            title,
                            style: const TextStyle(
                              fontSize: FontSizes.heading1,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 24,
                          ),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.white.withAlpha(51),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Content area
                  Flexible(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      child: SingleChildScrollView(
                        child: _buildRichHelpContent(context, content),
                      ),
                    ),
                  ),
                  // Footer with action button
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.check, size: 18),
                          label: const Text('Got it'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF1E88E5),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildRichHelpContent(BuildContext context, String content) {
    final paragraphs = content.split('\n\n');

    List<Widget> widgets = [];
    int sectionCount = 0;

    final List<Color> accentColors = [
      Colors.blue,
      Colors.purple,
      Colors.teal,
      Colors.indigo,
      Colors.cyan,
      Colors.deepPurple,
      Colors.lightBlue,
      Colors.green,
      Colors.amber.shade800,
    ];

    for (final paragraph in paragraphs) {
      if (paragraph.trim().isEmpty) continue;

      if (RegExp(r'^\d+\.').hasMatch(paragraph)) {
        final parts = paragraph.split(' ');
        final number = parts[0];
        final text = parts.sublist(1).join(' ');

        sectionCount++;

        widgets.add(
          Padding(
            padding: const EdgeInsets.only(top: 12.0, bottom: 6.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 28.0,
                  child: Text(
                    number,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: FontSizes.bodyLarge,
                      color: Color(0xFF1E88E5),
                    ),
                  ),
                ),
                const SizedBox(width: 12.0),
                Expanded(
                  child: Text(
                    text,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: FontSizes.bodyLarge,
                      color: Colors.black87,
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      } else if (paragraph.contains('• ') || paragraph.contains('   • ')) {
        final bulletItems = paragraph.split('\n');

        final colorIndex = (sectionCount - 1) % accentColors.length;
        final accentColor = accentColors[colorIndex];

        for (final item in bulletItems) {
          if (item.trim().isEmpty) continue;

          if (item.trim().startsWith('• ') || item.trim().startsWith('- ')) {
            final text = item.trim().substring(2);

            final bool isFeatureBullet =
                text.contains('Color') ||
                text.contains('Image') ||
                text.contains('Logo') ||
                text.contains('Background') ||
                text.contains('Toggle');

            widgets.add(
              Container(
                margin: const EdgeInsets.only(
                  bottom: 8.0,
                  left: 8.0,
                  right: 8.0,
                ),
                padding: const EdgeInsets.symmetric(
                  vertical: 8.0,
                  horizontal: 12.0,
                ),
                decoration:
                    isFeatureBullet
                        ? BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.0),
                          border: Border.all(color: accentColor.withAlpha(100)),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(5),
                              blurRadius: 2.0,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        )
                        : null,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.only(top: 3.0),
                      child: Icon(
                        isFeatureBullet ? Icons.check_circle : Icons.circle,
                        color: accentColor,
                        size: isFeatureBullet ? 18.0 : 10.0,
                      ),
                    ),
                    const SizedBox(width: 10.0),
                    Expanded(
                      child: Text(
                        text,
                        style: TextStyle(
                          fontSize:
                              isFeatureBullet
                                  ? FontSizes.bodyMedium
                                  : FontSizes.footnote,
                          fontWeight:
                              isFeatureBullet
                                  ? FontWeight.w600
                                  : FontWeight.normal,
                          height: 1.3,
                          letterSpacing: isFeatureBullet ? 0.2 : 0.0,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          } else if (item.contains('Tips for a Professional Profile:')) {
            widgets.add(
              Container(
                margin: const EdgeInsets.only(top: 24.0, bottom: 16.0),
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.amber.withAlpha(50),
                      Colors.orange.withAlpha(30),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 4.0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8.0),
                          decoration: BoxDecoration(
                            color: Colors.amber.shade600,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.lightbulb,
                            color: Colors.white,
                            size: 24.0,
                          ),
                        ),
                        const SizedBox(width: 12.0),
                        Expanded(
                          child: Text(
                            item.trim(),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: FontSizes.bodyLarge,
                              color: Colors.amber.shade800,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8.0),
                    Container(height: 2.0, color: Colors.amber.withAlpha(100)),
                  ],
                ),
              ),
            );
          } else {
            widgets.add(
              Padding(
                padding: const EdgeInsets.only(left: 16.0, bottom: 8.0),
                child: Text(
                  item.trim(),
                  style: TextStyle(
                    fontSize: FontSizes.bodyMedium,
                    height: 1.3,
                    color: Colors.black87,
                  ),
                ),
              ),
            );
          }
        }
      } else if (paragraph.contains('   • ')) {
        final subBulletItems = paragraph.split('\n');

        final colorIndex = (sectionCount - 1) % accentColors.length;
        final accentColor = accentColors[colorIndex];

        for (final item in subBulletItems) {
          if (item.trim().isEmpty) continue;

          final text =
              item.trim().startsWith('• ')
                  ? item.trim().substring(2)
                  : item.trim();

          final bool isKeyValue = text.contains(':');

          widgets.add(
            Container(
              margin: const EdgeInsets.only(bottom: 6.0, left: 40.0),
              padding:
                  isKeyValue
                      ? const EdgeInsets.symmetric(
                        vertical: 6.0,
                        horizontal: 10.0,
                      )
                      : null,
              decoration:
                  isKeyValue
                      ? BoxDecoration(
                        color: Colors.grey.withAlpha(15),
                        borderRadius: BorderRadius.circular(6.0),
                      )
                      : null,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6.0),
                    width: 6.0,
                    height: 6.0,
                    decoration: BoxDecoration(
                      color: accentColor.withAlpha(200),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8.0),
                  Expanded(
                    child:
                        isKeyValue
                            ? _buildKeyValueText(text, accentColor)
                            : Text(
                              text,
                              style: TextStyle(
                                fontSize: 10.0,
                                color: Colors.black87,
                                height: 1.3,
                                fontStyle: FontStyle.italic,
                              ),
                            ),
                  ),
                ],
              ),
            ),
          );
        }
      } else {
        widgets.add(
          Container(
            margin: const EdgeInsets.only(bottom: 12.0, top: 4.0),
            padding: const EdgeInsets.symmetric(
              horizontal: 12.0,
              vertical: 8.0,
            ),
            decoration: BoxDecoration(
              color: Colors.grey.withAlpha(15),
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.grey.withAlpha(50)),
            ),
            child: Text(
              paragraph,
              style: TextStyle(
                fontSize: FontSizes.bodySmall,
                height: 1.5,
                color: Colors.black87,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Widget _buildKeyValueText(String text, Color accentColor) {
    final parts = text.split(':');
    if (parts.length < 2) {
      return Text(
        text,
        style: TextStyle(
          fontSize: FontSizes.bodySmall,
          color: Colors.black87,
          height: 1.3,
        ),
      );
    }

    final key = '${parts[0]}:';
    final value = parts.sublist(1).join(':');

    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: key,
            style: TextStyle(
              fontSize: FontSizes.footnote,
              fontWeight: FontWeight.bold,
              color: accentColor,
              height: 1.3,
              letterSpacing: 0.2,
            ),
          ),
          TextSpan(
            text: value,
            style: TextStyle(
              fontSize: 10.0,
              color: Colors.black87,
              height: 1.3,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  void _showContactSupportDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: 16,
            backgroundColor: Colors.transparent,
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.98,
                maxWidth: MediaQuery.of(context).size.width * 0.99,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Colors.white, Colors.green.shade50],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withAlpha(76),
                    blurRadius: 20,
                    spreadRadius: 2,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.black.withAlpha(25),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header with gradient background
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [Colors.green.shade600, Colors.green.shade700],
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(51),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const FaIcon(
                            FontAwesomeIcons.headset,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Text(
                            'Contact Support',
                            style: TextStyle(
                              fontSize: FontSizes.heading1,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 0.5,
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 24,
                          ),
                          style: IconButton.styleFrom(
                            backgroundColor: Colors.white.withAlpha(51),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Content area
                  Flexible(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.blue.shade50,
                                    Colors.blue.shade100,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.blue.withAlpha(76),
                                ),
                              ),
                              child: const Row(
                                children: [
                                  Icon(
                                    Icons.support_agent,
                                    color: Colors.blue,
                                    size: 24,
                                  ),
                                  SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      'For additional help or account issues:',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: FontSizes.bodyLarge,
                                        color: Colors.blue,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 20),
                            _buildContactMethod(
                              context,
                              icon: FontAwesomeIcons.envelope,
                              title: 'Email',
                              detail: '<EMAIL>',
                            ),
                            const SizedBox(height: 16),
                            _buildContactMethod(
                              context,
                              icon: FontAwesomeIcons.whatsapp,
                              title: 'WhatsApp',
                              detail: '+20 ************',
                            ),
                            const SizedBox(height: 16),
                            _buildContactMethod(
                              context,
                              icon: FontAwesomeIcons.telegram,
                              title: 'Telegram',
                              detail: '+20 ************',
                            ),
                            const SizedBox(height: 16),
                            _buildContactMethod(
                              context,
                              icon: FontAwesomeIcons.facebook,
                              title: 'Facebook',
                              detail: 'SyvurSoft',
                            ),
                            const SizedBox(height: 24),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.orange.shade50,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.orange.withAlpha(76),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        color: Colors.orange,
                                        size: 20,
                                      ),
                                      SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'When contacting support, please include:',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.orange,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  _buildBulletPoint('Your full name'),
                                  _buildBulletPoint(
                                    'Email address used for registration',
                                  ),
                                  _buildBulletPoint('Device model'),
                                  _buildBulletPoint('Description of the issue'),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.purple.shade50,
                                    Colors.purple.shade100,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.purple.withAlpha(76),
                                ),
                              ),
                              child: const Row(
                                children: [
                                  Icon(
                                    Icons.schedule,
                                    color: Colors.purple,
                                    size: 20,
                                  ),
                                  SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      'Support hours: Sunday-Thursday, 9am-5pm (Egypt Time)',
                                      style: TextStyle(
                                        fontSize: FontSizes.bodyMedium,
                                        fontWeight: FontWeight.w500,
                                        color: Colors.purple,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Footer with action button
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.check, size: 18),
                          label: const Text('Got it'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green.shade600,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildContactMethod(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String detail,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withAlpha(25),
            borderRadius: BorderRadius.circular(8),
          ),
          child: FaIcon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 18,
          ),
        ),
        const SizedBox(width: 12),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: FontSizes.labelLarge,
              ),
            ),
            Text(
              detail,
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontSize: FontSizes.bodyLarge,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6),
            width: 6,
            height: 6,
            decoration: const BoxDecoration(
              color: Colors.orange,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: FontSizes.bodyMedium,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
