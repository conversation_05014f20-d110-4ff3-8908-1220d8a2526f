import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../screens/help_screen.dart';
import '../screens/settings_screen.dart';
import '../services/supabase_service.dart';
import '../screens/auth/login_screen.dart';
import '../constants/font_sizes.dart';

class AppDrawer extends StatefulWidget {
  const AppDrawer({super.key});

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      elevation: 4,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.fromLTRB(16, 48, 16, 16),
            color: Theme.of(context).colorScheme.primary,
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(50),
                        shape: BoxShape.circle,
                      ),
                      child: Image.asset(
                        'lib/assets/images/heartmain.png',
                        width: 40,
                        height: 40,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'ATRIA',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: FontSizes.heading1 + 4,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(30),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Text(
                    'Your Echo Partner',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: FontSizes.bodyLarge,
                    ),
                  ),
                ),
              ],
            ),
          ),

          _buildMenuItem(
            context,
            icon: FontAwesomeIcons.circleQuestion,
            title: 'Help',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const HelpScreen()),
              );
            },
          ),

          _buildMenuItem(
            context,
            icon: FontAwesomeIcons.gear,
            title: 'Settings',
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const SettingsScreen()),
              );
            },
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Divider(color: Colors.grey.shade300, thickness: 1),
          ),

          _buildMenuItem(
            context,
            icon: FontAwesomeIcons.fileContract,
            title: 'Terms of Service',
            onTap: () {
              Navigator.pop(context);
              _showLegalDialog(
                context,
                'Terms of Service',
                'By creating an account, accessing, or using the Service, you acknowledge that you have read, understood, and agree to be bound by these Terms and our Privacy Policy, which is incorporated by reference into these Terms. If you do not agree to these Terms, you may not use the Service.\n\n'
                    '1-Description of the Service\n\n'
                    '• ATRIA is a software tool designed to assist qualified medical professionals in their work. The Service is provided as a tool to support, not replace, professional medical practices.\n\n'
                    '• CRITICAL ACKNOWLEDGEMENT: ATRIA is an assistive tool and is not a substitute for professional medical judgment, diagnosis, or treatment. You, as a qualified medical professional, are solely responsible for all medical decisions and patient outcomes.\n\n'
                    '2-Eligibility and User Responsibilities\n\n'
                    '• Eligibility: You represent and warrant that you are a qualified medical professional and are legally permitted to use the Service in your jurisdiction.\n\n'
                    '• Account Security: You are responsible for maintaining the confidentiality of your account login information and are fully responsible for all activities that occur under your account. You agree to immediately notify us of any unauthorized use or security breach.\n\n'
                    '• Data Accuracy: You are solely responsible for the accuracy, legality, and appropriateness of all data you enter into the Service and for the interpretation and application of any reports or information generated by the Service.\n\n'
                    '• Legal Compliance: You agree to use the Service in compliance with all applicable local, state, national, and international laws, rules, and regulations, including but not limited to healthcare data privacy laws (such as HIPAA, GDPR, etc.) and professional codes of conduct.\n\n'
                    '3-Acceptable Use and Prohibited Conduct\n\n'
                    '• You agree not to:\n\n'
                    '• Use the Service for any illegal or unauthorized purpose.\n\n'
                    '• Reverse engineer, decompile, disassemble, or attempt to extract the source code of the Service.\n\n'
                    '• Use the Service to store or transmit any data that is unlawful, infringing, or violates any third-party’s rights.\n\n'
                    '• Upload or transmit any malicious code, viruses, or worms.\n\n'
                    '• Attempt to gain unauthorized access to the Service, other users accounts, or our computer systems.\n\n'
                    '• Use the Service in any manner that could damage, disable, overburden, or impair the Service.\n\n'
                    '4-Fees, Payment, and Subscriptions\n\n'
                    '• Subscription: Access to certain features of the Service may require a paid subscription. All subscription fees are charged in advance on a recurring basis (e.g., monthly or annually).\n\n'
                    '• Automatic Renewal: Your subscription will automatically renew at the end of each billing cycle unless you cancel it through your account settings or by contacting us.\n\n'
                    '• Fee Changes: We reserve the right to change our subscription fees at any time. We will provide you with reasonable prior notice of any price changes.\n\n'
                    '5-Refund and Cancellation Policy\n\n'
                    '5-1• General Policy: All subscription fees are charged in advance and are non-refundable except as expressly set forth herein or as required by applicable law.\n\n'
                    '5-2• New Subscriber Refund: New subscribers may cancel their subscription within fourteen (14) days of initial purchase to receive a full refund, provided no substantial use of premium features has occurred.\n\n'
                    '5-3• Technical Service Failures: If you experience persistent technical issues that prevent normal use of the application despite reasonable troubleshooting efforts, you may request a refund within thirty (30) days of the issue first occurring. Such requests require documentation of the technical problem and evidence of attempted resolution.\n\n'
                    '5-4• Refund Processing: Approved refunds will be processed within seven (7) to ten (10) business days and credited to the original payment method.\n\n'
                    '5-5•Pro-rated Refunds: In exceptional circumstances and at our sole discretion, we may provide pro-rated refunds for unused portions of subscription periods. Such refunds are not guaranteed and will be evaluated on a case-by-case basis.\n\n'
                    '5-6• Refund Requests: All refund requests must be submitted in <NAME_EMAIL> or via WhatsApp at +20 ************, including your account details and reason for the request.\n\n'
                    '5-7• Third-Party Platform Purchases: Subscriptions purchased through third-party platforms (e.g., Google Play Store, Apple App Store) are subject to the respective platform\'s refund policies. We cannot process refunds for such purchases directly.\n\n'
                    '5-8• Statutory Rights: This refund policy does not affect your statutory rights as a consumer.\n\n'
                    '6-Privacy Policy\n\n'
                    '• Your privacy is critically important to us. Our Privacy Policy explains how we collect, use, store, and protect your personal and other data. By agreeing to these Terms, you also agree to the terms of our Privacy Policy.\n\n'
                    '7-Intellectual Property\n\n'
                    '• The Service and its original content, features, and functionality are and will remain the exclusive property of Syvursoft and its licensors. The Service is protected by copyright, trademark, and other laws. Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of Syvursoft.\n\n'
                    '8-Disclaimer of Warranties\n\n'
                    '• THE SERVICE IS PROVIDED ON AN "AS IS" AND "AS AVAILABLE" BASIS, WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, OR NON-INFRINGEMENT. SYVURSOFT DOES NOT WARRANT THAT THE SERVICE WILL BE UNINTERRUPTED, SECURE, ERROR-FREE, OR THAT ANY REPORTS OR DATA WILL BE ACCURATE OR RELIABLE.\n\n'
                    '9-Limitation of Liability\n\n'
                    '• TO THE FULLEST EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT SHALL SYVURSOFT, ITS AFFILIATES, DIRECTORS, EMPLOYEES, OR AGENTS BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM (I) YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE SERVICE; (II) ANY CONDUCT OR CONTENT OF ANY THIRD PARTY ON THE SERVICE; (III) ANY CONTENT OBTAINED FROM THE SERVICE; AND (IV) UNAUTHORIZED ACCESS, USE, OR ALTERATION OF YOUR TRANSMISSIONS OR CONTENT, WHETHER BASED ON WARRANTY, CONTRACT, TORT (INCLUDING NEGLIGENCE), OR ANY OTHER LEGAL THEORY.\n\n'
                    '• OUR TOTAL CUMULATIVE LIABILITY TO YOU FOR ANY AND ALL CLAIMS ARISING FROM OR RELATING TO THE SERVICE SHALL NOT EXCEED THE GREATER OF (A) ONE HUNDRED U.S. DOLLARS (\$100.00) OR (B) THE TOTAL AMOUNT OF FEES, IF ANY, THAT YOU PAID TO US FOR THE SERVICE IN THE SIX (6) MONTHS PRIOR TO THE ACTION GIVING RISE TO LIABILITY.\n\n'
                    '10-Indemnification\n\n'
                    '• You agree to defend, indemnify, and hold harmless Syvursoft and its licensee and licensors, and their employees, contractors, agents, officers, and directors, from and against any and all claims, damages, obligations, losses, liabilities, costs or debt, and expenses (including but not limited to attorney\'s fees), resulting from or arising out of a) your use and access of the Service, or b) a breach of these Terms.\n\n'
                    '11-Termination\n\n'
                    '• We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, at our sole discretion, for any reason whatsoever, including but not limited to a breach of these Terms. Upon termination, your right to use the Service will immediately cease.\n\n'
                    '12-Modifications to the Terms\n\n'
                    '• We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect. By continuing to access or use our Service after any revisions become effective, you agree to be bound by the revised terms.\n\n'
                    '13-Governing Law and Jurisdiction\n\n'
                    '• These Terms shall be governed and construed in accordance with the laws of the Arab Republic of Egypt, without regard to its conflict of law provisions. You agree to submit to the exclusive jurisdiction of the courts located in Cairo, Egypt to resolve any legal matter arising from the Terms.\n\n'
                    '14-General Provisions\n\n'
                    '• Entire Agreement: These Terms and our Privacy Policy constitute the entire agreement between you and Syvursoft concerning the Service.\n\n'
                    '• Severability: If any provision of these Terms is held to be invalid or unenforceable, the remaining provisions of these Terms will remain in effect.\n\n'
                    '• No Waiver: No waiver of any term of these Terms shall be deemed a further or continuing waiver of such term or any other term.\n\n'
                    '15-Contact Information\n\n'
                    '• If you have any questions about these Terms, please contact us at:\n'
                    'Email:<EMAIL>\n'
                    'WhatsApp/Phone: +20 ************\n'
                    'Facebook: SyvurSoft\n'
                    'GitHub: github.com/Waleedsheha/Syvursoft\n\n'
                    'Thank you for using ATRIA. We hope you find it a valuable tool in your practice.',
              );
            },
          ),

          _buildMenuItem(
            context,
            icon: FontAwesomeIcons.shieldHalved,
            title: 'Privacy Policy',
            onTap: () {
              Navigator.pop(context);
              _showLegalDialog(
                context,
                'Privacy Policy',
                'ATRIA Privacy Policy:\n\n'
                    '1. Data Controller: SyvurSoft (<EMAIL>) is the data controller responsible for your personal information.\n\n'
                    '2. Information We Collect:\n'
                    '   • Account information (email,username,Real name if provided in signup screen)\n'
                    '   • Subscription and payment information\n\n'
                    '3. How We Use Your Information:\n'
                    '   • To authenticate your account and manage subscriptions\n'
                    '   • To provide customer support\n\n'
                    '4. Data Storage and Security:\n'
                    '   • Patient data is stored locally on your device only\n'
                    '   • Account data is securely stored with Supabase (encrypted)\n'
                    '   • Subscription data is managed by RevenueCat\n'
                    '   • We use industry-standard encryption and security measures\n\n'
                    '5. Third-Party Services:\n'
                    '   • Supabase: Authentication and account management\n'
                    '   • RevenueCat: Subscription and payment processing\n'
                    '   • These services have their own privacy policies\n\n'
                    '6. Data Sharing:\n'
                    '   • We do not sell or share your personal data\n'
                    '   • Patient data never leaves your device\n\n'
                    '7. Your Rights:\n'
                    '   • Access your personal data\n'
                    '   • Request data correction or deletion\n'
                    '   • Export your data\n'
                    '   • Withdraw consent at any time\n\n'
                    '8. Healthcare Compliance:\n'
                    '   • You are responsible for HIPAA compliance when applicable\n'
                    '   • Obtain appropriate patient consent before data entry\n'
                    '   • Maintain device security and access controls\n\n'
                    '9. Data Retention:\n'
                    '   • Patient data: Retained locally until you delete it\n'
                    '   • Account data: Retained while account is active\n'
                    '10. International Users:\n'
                    '    • Services provided globally with data protection safeguards\n'
                    '    • EU users have additional rights under GDPR\n\n'
                    '11. Children\'s Privacy:\n'
                    '    • ATRIA is not intended for users under 18 as they are not qualified medical professionals\n'
                    '    • We do not knowingly collect data from minors\n\n'
                    '12. Policy Updates:\n'
                    '    • We will notify you of significant changes\n'
                    '    • Continued use constitutes acceptance of updates\n\n'
                    '13. Contact Us:\n'
                    '    • Privacy questions: <EMAIL>\n'
                    '    • WhatsApp: +20 ************\n\n'
                    '14. Governing Law:\n'
                    '    • This policy is governed by applicable data protection laws\n'
                    '    • Disputes resolved according to your local jurisdiction',
              );
            },
          ),

          _buildMenuItem(
            context,
            icon: FontAwesomeIcons.fileSignature,
            title: 'User Agreement',
            onTap: () {
              Navigator.pop(context);
              _showLegalDialog(
                context,
                'User Agreement',
                'ATRIA User Agreement:\n\n'
                    '1. License: We grant you a limited, non-exclusive license to use ATRIA.\n\n'
                    '2. Restrictions: You may not distribute, modify, or create derivative works.\n\n'
                    '3. Ownership: All rights, title, and interest in ATRIA remain with us.\n\n'
                    '4. Termination: We may terminate your access if you violate these terms.\n\n'
                    '5. Disclaimer: ATRIA is not a substitute for professional medical judgment.\n\n'
                    '6. Limitation of Liability: We are not liable for any damages arising from use.\n\n'
                    '7. Governing Law: This agreement is governed by applicable laws.',
              );
            },
          ),

          _buildMenuItem(
            context,
            icon: FontAwesomeIcons.circleInfo,
            title: 'About',
            onTap: () {
              Navigator.pop(context);
              _showAboutDialog(context);
            },
          ),

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Divider(color: Colors.grey.shade300, thickness: 1),
          ),

          _buildMenuItem(
            context,
            icon: FontAwesomeIcons.rightFromBracket,
            title: 'Sign Out',
            onTap: () {
              showDialog(
                context: context,
                builder: (BuildContext dialogContext) {
                  return AlertDialog(
                    title: const Text('Sign Out'),
                    content: const Text('Are you sure you want to sign out?'),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    actions: [
                      TextButton(
                        child: const Text('Cancel'),
                        onPressed: () {
                          Navigator.of(dialogContext).pop();
                        },
                      ),
                      TextButton(
                        child: const Text('Sign Out'),
                        onPressed: () async {
                          try {
                            Navigator.of(dialogContext).pop();

                            await Future.wait([SupabaseService.signOut()]);

                            debugPrint('✅ Signed out from Supabase');

                            if (context.mounted) {
                              Navigator.of(context).pushAndRemoveUntil(
                                MaterialPageRoute(
                                  builder: (context) => const LoginScreen(),
                                ),
                                (route) => false,
                              );
                            }
                          } catch (e) {
                            debugPrint('⚠️ Sign out error: $e');
                            if (context.mounted) {
                              Navigator.of(context).pushAndRemoveUntil(
                                MaterialPageRoute(
                                  builder: (context) => const LoginScreen(),
                                ),
                                (route) => false,
                              );
                            }
                          }
                        },
                      ),
                    ],
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        fullscreenDialog: true,
        builder:
            (context) => Scaffold(
              appBar: AppBar(title: const Text('About ATRIA')),
              body: SafeArea(
                child: Container(
                  color: Colors.white,
                  child: ListView(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(24),
                        alignment: Alignment.center,
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Theme.of(
                                  context,
                                ).colorScheme.primary.withAlpha(20),
                                shape: BoxShape.circle,
                              ),
                              child: Image.asset(
                                'lib/assets/images/heartmain.png',
                                width: 60,
                                height: 60,
                              ),
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'ATRIA',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: FontSizes.heading1,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Your Echo Partner',
                              style: TextStyle(
                                fontSize: FontSizes.bodyLarge,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const Divider(),

                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Our Mission',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: FontSizes.heading3,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Transforming complex medical data into intuitive, actionable insights for healthcare professionals worldwide.',
                              style: TextStyle(
                                fontSize: FontSizes.bodyMedium,
                                height: 1.5,
                                color: Colors.grey.shade800,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const Divider(),

                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Our Story',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: FontSizes.heading3,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Doctor Waleed Sheha started SyvurSoft after working for years in busy emergency rooms and ICUs. As a doctor, I kept facing the same problem - we had to make quick decisions with complex patient data, especially blood gas results.\n\n'
                              'I looked for tools to help with this, but couldn\'t find anything that was both accurate and easy to use in high-pressure situations. So I decided to build one myself.\n\n'
                              'This led to ABGen, our first app that helps doctors quickly understand blood gas results. We designed it to be simple enough to use during emergencies but detailed enough to provide valuable insights.\n\n'
                              'Building on the success of ABGen, we developed ATRIA as our second app to address another critical need in clinical practice. ATRIA focuses on echocardiography reporting, helping healthcare professionals create comprehensive, accurate echo reports efficiently.\n\n'
                              'Just like ABGen, ATRIA combines clinical expertise with user-friendly design. It provides detailed reference values, intelligent templates, and streamlined workflows that work seamlessly in busy clinical environments. Our goal remains the same: creating tools that enhance clinical decision-making without adding complexity to your workflow.',
                              style: TextStyle(
                                fontSize: FontSizes.bodyMedium,
                                height: 1.5,
                                color: Colors.grey.shade800,
                              ),
                            ),
                          ],
                        ),
                      ),

                      const Divider(),

                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Our Approach',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: FontSizes.heading3,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'At SyvurSoft, we combine medical expertise with technological innovation to create software that truly serves healthcare professionals:',
                              style: TextStyle(
                                fontSize: FontSizes.bodyMedium,
                                height: 1.5,
                                color: Colors.grey.shade800,
                              ),
                            ),
                            const SizedBox(height: 16),
                            _buildApproachItem(
                              context,
                              title: 'Clinical Expertise',
                              description:
                                  'Founded by a practicing physician with real-world clinical experience in critical care and emergency medicine. Our solutions are built from the ground up with deep medical knowledge.',
                            ),
                            _buildApproachItem(
                              context,
                              title: 'User-Centered Design',
                              description:
                                  'Our applications are built with the clinician\'s workflow in mind, ensuring seamless integration into clinical practice. We prioritize intuitive interfaces that work in high-pressure environments.',
                            ),
                            _buildApproachItem(
                              context,
                              title: 'Educational Focus',
                              description:
                                  'We\'re committed to not just providing answers, but enhancing understanding through interactive learning tools. Every feature is designed to build clinical reasoning skills.',
                            ),
                          ],
                        ),
                      ),

                      const Divider(),

                      Padding(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'Contact Us',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: FontSizes.heading3,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Have questions about our products? We\'d love to hear from you.',
                              style: TextStyle(
                                fontSize: FontSizes.bodyMedium,
                                height: 1.5,
                                color: Colors.grey.shade800,
                              ),
                            ),
                            const SizedBox(height: 16),
                            _buildContactItem(
                              context,
                              icon: FontAwesomeIcons.envelope,
                              title: 'Email',
                              detail: '<EMAIL>',
                            ),
                            _buildContactItem(
                              context,
                              icon: FontAwesomeIcons.whatsapp,
                              title: 'WhatsApp',
                              detail: '+20 ************',
                            ),
                            _buildContactItem(
                              context,
                              icon: FontAwesomeIcons.telegram,
                              title: 'Telegram',
                              detail: '+20 ************',
                            ),
                            _buildContactItem(
                              context,
                              icon: FontAwesomeIcons.facebook,
                              title: 'Facebook',
                              detail: 'SyvurSoft',
                            ),
                            _buildContactItem(
                              context,
                              icon: FontAwesomeIcons.github,
                              title: 'GitHub',
                              detail: 'github.com/Waleedsheha/Syvursoft',
                            ),
                          ],
                        ),
                      ),

                      Container(
                        padding: const EdgeInsets.all(24),
                        alignment: Alignment.center,
                        child: Column(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Theme.of(
                                  context,
                                ).colorScheme.primary.withAlpha(20),
                                shape: BoxShape.circle,
                              ),
                              child: Image.asset(
                                'lib/assets/images/heartmain.png',
                                width: 60,
                                height: 60,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              '© ${DateTime.now().year} SyvurSoft. All Rights Reserved.',
                              style: TextStyle(
                                fontSize: FontSizes.footnote,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
      ),
    );
  }

  Widget _buildApproachItem(
    BuildContext context, {
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: FontSizes.bodyLarge,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: TextStyle(
              fontSize: FontSizes.bodySmall,
              height: 1.5,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String detail,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withAlpha(20),
              shape: BoxShape.circle,
            ),
            child: FaIcon(
              icon,
              color: Theme.of(context).colorScheme.primary,
              size: 16,
            ),
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: FontSizes.bodyMedium,
                ),
              ),
              Text(
                detail,
                style: TextStyle(
                  fontSize: FontSizes.bodySmall,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showLegalDialog(BuildContext context, String title, String content) {
    Navigator.of(context).push(
      MaterialPageRoute(
        fullscreenDialog: true,
        builder:
            (context) => Scaffold(
              appBar: AppBar(title: Text(title)),
              body: SafeArea(
                child: Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.grey.shade200,
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(
                                  context,
                                ).colorScheme.primary.withAlpha(20),
                                shape: BoxShape.circle,
                              ),
                              child: Image.asset(
                                'lib/assets/images/heartmain.png',
                                width: 60,
                                height: 60,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'ATRIA',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: FontSizes.bodyLarge,
                                  ),
                                ),
                                Text(
                                  'Effective: January 1, ${DateTime.now().year}',
                                  style: TextStyle(
                                    fontSize: FontSizes.footnote,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      Expanded(
                        child: ListView(
                          padding: const EdgeInsets.all(16),
                          children: [
                            Text(
                              'Thank you for using ATRIA. These ${title.toLowerCase()} govern your relationship with ATRIA and use of our services.',
                              style: const TextStyle(
                                fontSize: FontSizes.bodyMedium,
                                height: 1.5,
                              ),
                            ),
                            const SizedBox(height: 24),

                            _buildLegalContent(content),

                            const SizedBox(height: 24),

                            Text(
                              'If you have any questions about these ${title.toLowerCase()}, please contact us.',
                              style: TextStyle(
                                fontSize: FontSizes.bodySmall,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
      ),
    );
  }

  Widget _buildLegalContent(String content) {
    final paragraphs = content.split('\n\n');
    List<Widget> widgets = [];

    for (final paragraph in paragraphs) {
      if (paragraph.trim().isEmpty) continue;

      if (RegExp(r'^\d+\.').hasMatch(paragraph)) {
        final parts = paragraph.split(' ');
        final number = parts[0].replaceAll('.', '');
        final text = parts.sublist(1).join(' ');

        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 24,
                  child: Text(
                    number,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: FontSizes.bodyMedium,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        text,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: FontSizes.bodyMedium,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      } else {
        widgets.add(
          Padding(
            padding: const EdgeInsets.only(bottom: 16.0, left: 24.0),
            child: Text(
              paragraph,
              style: const TextStyle(
                fontSize: FontSizes.bodySmall,
                height: 1.5,
                color: Colors.black87,
              ),
            ),
          ),
        );
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets,
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    String? subtitle,
  }) {
    final Color iconBgColor = Theme.of(
      context,
    ).colorScheme.primary.withAlpha(20);
    final Color iconColor = Theme.of(context).colorScheme.primary;

    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: iconBgColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: FaIcon(icon, color: iconColor, size: 16),
      ),
      title: Text(
        title,
        style: TextStyle(
          color: Colors.grey.shade800,
          fontWeight: FontWeight.w500,
          fontSize: FontSizes.labelLarge,
        ),
      ),
      subtitle:
          subtitle != null
              ? Text(
                subtitle,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: FontSizes.bodySmall,
                ),
              )
              : null,
      trailing: const FaIcon(
        FontAwesomeIcons.angleRight,
        color: Colors.grey,
        size: 14,
      ),
      onTap: onTap,
    );
  }
}
