import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/foundation.dart';

import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:app_links/app_links.dart';
import 'screens/home_page.dart';

import 'screens/auth/login_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/smart_templates_screen.dart';

import 'services/user_profile_service.dart';
import 'services/supabase_service.dart';
import 'services/secure_config_service.dart';
import 'utils/setup_secure_config.dart';

import 'constants/font_sizes.dart';

final GlobalKey<NavigatorState> globalNavigatorKey =
    GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Add a small delay to ensure window is properly initialized on Windows
  if (defaultTargetPlatform == TargetPlatform.windows) {
    await Future.delayed(const Duration(milliseconds: 100));
  }

  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Color.fromARGB(0, 255, 255, 255),
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Set up secure configuration if not already present
  if (!await SecureConfigService.hasConfiguration()) {
    await SetupSecureConfig.setupConfiguration();
  }

  // Get configuration values from secure storage
  final supabaseUrl = await SecureConfigService.getSupabaseUrl();
  final supabaseAnonKey = await SecureConfigService.getSupabaseAnonKey();

  await Supabase.initialize(
    url: supabaseUrl,
    anonKey: supabaseAnonKey,
    debug: false,
    authOptions: const FlutterAuthClientOptions(
      authFlowType: AuthFlowType.pkce,
    ),
  );

  // Initialize services
  await UserProfileService.getUserProfile();

  runApp(const MainApp());
}

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _setupDeepLinkHandling();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Only clear sessions when app is completely terminated, not just backgrounded
    if (state == AppLifecycleState.detached) {
      _clearSessionsOnAppTermination();
    }
  }

  Future<void> _clearSessionsOnAppTermination() async {
    try {
      // Only sign out if user is actually signed in
      if (SupabaseService.isSignedIn) {
        await SupabaseService.signOut();
        debugPrint('🔒 Supabase signed out on app termination');
      }
    } catch (e) {
      debugPrint('⚠️ Error during session clearing on app termination: $e');
    }
  }

  Future<void> _setupDeepLinkHandling() async {
    final appLinks = AppLinks();

    try {
      final initialUri = await appLinks.getInitialLink();
      if (initialUri != null) {
        _handleDeepLink(initialUri);
      }
    } catch (e) {}

    appLinks.uriLinkStream.listen((uri) {
      _handleDeepLink(uri);
    }, onError: (error) {});
  }

  Future<void> _handleDeepLink(Uri uri) async {
    final isAuthLink = await SupabaseService.handleDeepLink(uri);

    if (isAuthLink) {
      await Future.delayed(const Duration(milliseconds: 500));

      if (uri.toString().contains('type=recovery')) {
        globalNavigatorKey.currentState?.pushNamedAndRemoveUntil(
          '/reset-password',
          (route) => false,
        );
      } else {
        // For OAuth success (like Google Sign-In), navigate to home
        globalNavigatorKey.currentState?.pushNamedAndRemoveUntil(
          '/home',
          (route) => false,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'ATRIA',
      navigatorKey: globalNavigatorKey,

      highContrastTheme: null,
      highContrastDarkTheme: null,

      debugShowMaterialGrid: false,
      showPerformanceOverlay: false,
      showSemanticsDebugger: false,

      scrollBehavior: const MaterialScrollBehavior().copyWith(
        dragDevices: {
          PointerDeviceKind.mouse,
          PointerDeviceKind.touch,
          PointerDeviceKind.stylus,
          PointerDeviceKind.unknown,
        },
      ),

      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      supportedLocales: const [Locale('en'), Locale('ar')],
      theme: ThemeData(
        colorScheme: const ColorScheme(
          primary: Color(0xFF1E88E5),
          primaryContainer: Color(0xFF0D47A1),
          secondary: Color.fromARGB(255, 0, 143, 59),
          secondaryContainer: Color(0xFF004D40),
          surface: Color(0xFFF5F5F5),
          surfaceContainerHighest: Color(0xFFFFFFFF),
          error: Color(0xFFD50000),
          onPrimary: Color(0xFFFFFFFF),
          onSecondary: Color(0xFFFFFFFF),
          onSurface: Color(0xFF212121),
          onError: Color(0xFFFFFFFF),
          brightness: Brightness.light,
          tertiary: Color.fromARGB(255, 0, 0, 0),
          tertiaryContainer: Color.fromARGB(255, 177, 62, 0),
        ),
        useMaterial3: true,

        fontFamily: 'NotoNaskhArabic',
        appBarTheme: AppBarTheme(
          backgroundColor: const Color(0xFF1E88E5),
          foregroundColor: Colors.white,
          elevation: 10,
          centerTitle: true,
          shadowColor: const Color(0x99000000),
          titleTextStyle: TextStyle(
            fontSize: FontSizes.heading1,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
          ),
        ),
        cardTheme: CardThemeData(
          elevation: 10,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          clipBehavior: Clip.antiAlias,
          color: Colors.white,
          shadowColor: Colors.black.withAlpha(40),
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        ),
        textTheme: TextTheme(
          headlineLarge: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: FontSizes.heading2,
            letterSpacing: 0.5,
            color: const Color(0xFF1E88E5),
          ),
          headlineMedium: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: FontSizes.heading3,
            letterSpacing: 0.5,
            color: const Color(0xFF1E88E5),
          ),
          titleLarge: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: FontSizes.heading3,
            letterSpacing: 0.3,
            color: const Color(0xFF1E88E5),
          ),
          bodyLarge: TextStyle(
            fontSize: FontSizes.bodyLarge,
            color: const Color(0xFF424242),
          ),
          bodyMedium: TextStyle(
            fontSize: FontSizes.bodyMedium,
            color: const Color(0xFF616161),
          ),
          bodySmall: TextStyle(
            fontSize: FontSizes.bodySmall,
            color: const Color(0xFF757575),
          ),
          labelLarge: TextStyle(
            fontSize: FontSizes.labelLarge,
            fontWeight: FontWeight.w500,
          ),
          labelMedium: TextStyle(
            fontSize: FontSizes.labelMedium,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
      routes: {
        '/splash': (context) => const SplashScreen(),

        '/login': (context) => const LoginScreen(),
        '/home': (context) => const HomePage(),
        '/smart-templates': (context) => const SmartTemplatesScreen(),
      },
    );
  }
}
