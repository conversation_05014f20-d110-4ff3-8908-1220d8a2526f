import 'base_interpretation.dart';

class InferiorVenaCavaInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Inferior vena cava assessment was not performed.';
    }

    final ivc = data['ivc'] ?? {};
    final ivcDiameter = _parseDouble(ivc['diameter']);
    final ivcCollapsibility = ivc['collapsibility'] as String? ?? 'Normal';

    if (ivcDiameter != null) {
      String lowerCaseCollapsibility = ivcCollapsibility.toLowerCase();

      if (lowerCaseCollapsibility == "none") {
        return 'The inferior vena cava diameter is $ivcDiameter cm with normal collapsibility.';
      }

      final String ivcInterpretation = _interpretIVC(
        ivcDiameter,
        ivcCollapsibility,
      );
      final String collapsibilityDisplay = _getCollapsibilityDisplayText(
        ivcCollapsibility,
      );
      return 'The inferior vena cava is $ivcInterpretation (Diameter: $ivcDiameter cm, Collapsibility: $collapsibilityDisplay).';
    } else {
      return 'The inferior vena cava was not measured.';
    }
  }

  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  String _getCollapsibilityDisplayText(String collapsibility) {
    String lowerCase = collapsibility.toLowerCase();
    switch (lowerCase) {
      case 'normal':
        return '≥50% (Normal)';
      case 'reduced':
        return '<50% (Reduced)';
      case 'absent':
        return 'Absent';
      default:
        return collapsibility;
    }
  }

  String _interpretIVC(double diameter, String collapsibility) {
    String lowerCaseCollapsibility = collapsibility.toLowerCase();

    if (diameter <= 2.1 && lowerCaseCollapsibility == 'normal') {
      return 'normal size with normal collapsibility, suggesting normal RA pressure (0-5 mmHg)';
    } else if (diameter <= 2.1 &&
        (lowerCaseCollapsibility == 'reduced' ||
            lowerCaseCollapsibility == 'absent')) {
      return 'normal size with reduced collapsibility, suggesting intermediate RA pressure (6-10 mmHg)';
    } else if (diameter > 2.1 && lowerCaseCollapsibility == 'normal') {
      return 'dilated with normal collapsibility, suggesting intermediate RA pressure (6-10 mmHg)';
    } else if (diameter > 2.1 &&
        (lowerCaseCollapsibility == 'reduced' ||
            lowerCaseCollapsibility == 'absent')) {
      return 'dilated with reduced collapsibility, suggesting high RA pressure (11-20 mmHg)';
    } else {
      return 'Diameter: $diameter cm, Collapsibility: $collapsibility';
    }
  }
}
