import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/report_model.dart';
import 'package:flutter/foundation.dart';

class ReportStorageService {
  static const String _reportsKey = 'saved_reports';
  static const String _reportIdsKey = 'report_ids';

  static Future<bool> saveReport(Report report) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if we're on web and implement storage limits
      if (kIsWeb) {
        return await _saveReportWeb(report, prefs);
      }

      final reportIds = prefs.getStringList(_reportIdsKey) ?? [];

      if (!reportIds.contains(report.id)) {
        reportIds.add(report.id);
        await prefs.setStringList(_reportIdsKey, reportIds);
      }

      final reportJson = report.toJson();

      if (report.pdfBytes != null) {
        await prefs.setString(
          '${_reportsKey}_${report.id}_pdf',
          base64Encode(report.pdfBytes!),
        );
        reportJson.remove('pdfBytes');
      }

      await prefs.setString(
        '${_reportsKey}_${report.id}',
        jsonEncode(reportJson),
      );

      return true;
    } catch (e) {
      debugPrint('Error saving report: $e');
      return false;
    }
  }

  static Future<bool> _saveReportWeb(
    Report report,
    SharedPreferences prefs,
  ) async {
    try {
      // For web, limit the number of stored reports to prevent quota issues
      const maxReports = 3; // Limit to 3 reports on web

      final reportIds = prefs.getStringList(_reportIdsKey) ?? [];

      // If we're at the limit, remove the oldest report
      if (reportIds.length >= maxReports && !reportIds.contains(report.id)) {
        final oldestId = reportIds.first;
        await _deleteReportData(oldestId, prefs);
        reportIds.removeAt(0);
      }

      if (!reportIds.contains(report.id)) {
        reportIds.add(report.id);
        await prefs.setStringList(_reportIdsKey, reportIds);
      }

      final reportJson = report.toJson();

      // For web, don't store PDF bytes to save space
      // Instead, regenerate them when needed
      reportJson.remove('pdfBytes');

      await prefs.setString(
        '${_reportsKey}_${report.id}',
        jsonEncode(reportJson),
      );

      debugPrint('Report saved on web without PDF bytes to save storage space');
      return true;
    } catch (e) {
      debugPrint('Error saving report on web: $e');
      return false;
    }
  }

  static Future<void> _deleteReportData(
    String id,
    SharedPreferences prefs,
  ) async {
    await prefs.remove('${_reportsKey}_$id');
    await prefs.remove('${_reportsKey}_${id}_pdf');
  }

  static Future<List<Report>> getAllReports() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reportIds = prefs.getStringList(_reportIdsKey) ?? [];

      final reports = <Report>[];

      for (final id in reportIds) {
        final reportJson = prefs.getString('${_reportsKey}_$id');
        if (reportJson != null) {
          final reportData = jsonDecode(reportJson) as Map<String, dynamic>;

          // On web, PDF bytes are not stored to save space
          if (!kIsWeb) {
            final pdfBase64 = prefs.getString('${_reportsKey}_${id}_pdf');
            if (pdfBase64 != null) {
              reportData['pdfBytes'] = base64Decode(pdfBase64);
            }
          }

          reports.add(Report.fromJson(reportData));
        }
      }

      reports.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return reports;
    } catch (e) {
      debugPrint('Error getting reports: $e');
      return [];
    }
  }

  static Future<Report?> getReportById(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final reportJson = prefs.getString('${_reportsKey}_$id');

      if (reportJson != null) {
        final reportData = jsonDecode(reportJson) as Map<String, dynamic>;

        // On web, PDF bytes are not stored to save space
        if (!kIsWeb) {
          final pdfBase64 = prefs.getString('${_reportsKey}_${id}_pdf');
          if (pdfBase64 != null) {
            reportData['pdfBytes'] = base64Decode(pdfBase64);
          }
        }

        return Report.fromJson(reportData);
      }

      return null;
    } catch (e) {
      debugPrint('Error getting report: $e');
      return null;
    }
  }

  static Future<bool> deleteReport(String id) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final reportIds = prefs.getStringList(_reportIdsKey) ?? [];
      reportIds.remove(id);
      await prefs.setStringList(_reportIdsKey, reportIds);

      await prefs.remove('${_reportsKey}_$id');

      await prefs.remove('${_reportsKey}_${id}_pdf');

      return true;
    } catch (e) {
      debugPrint('Error deleting report: $e');
      return false;
    }
  }
}
