import 'package:flutter/material.dart';
import '../services/supabase_service.dart';
import '../screens/auth/login_screen.dart';

class AuthWrapper extends StatefulWidget {
  final Widget child;

  const AuthWrapper({
    super.key,
    required this.child,
  });

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  bool _isLoading = true;
  bool _isAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      
      final isOnlineAuthenticated = SupabaseService.isSignedIn;
      if (isOnlineAuthenticated) {
        
        final isSessionValid = await SupabaseService.verifySession();
        setState(() {
          _isAuthenticated = isSessionValid;
          _isLoading = false;
        });
      } else {
        
        setState(() {
          _isAuthenticated = false;
          _isLoading = false;
        });
      }
    } catch (e) {
      
      setState(() {
        _isAuthenticated = false;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    
    if (_isAuthenticated) {
      return widget.child;
    }

    
    return const LoginScreen();
  }
}
