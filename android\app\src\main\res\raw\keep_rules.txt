# Enhanced Flutter R8 Security Rules
# Optimized for Atria medical app with comprehensive protection

# ============================================================================
# FLUTTER CORE PROTECTION (Refined for better security)
# ============================================================================

# Keep essential Flutter framework classes
-keep class io.flutter.app.** { *; }
-keep class io.flutter.embedding.** { *; }
-keep class io.flutter.plugin.common.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.util.** { *; }

# Keep Dart VM services
-keep class io.flutter.vm.service.** { *; }

# Keep Flutter deferred component classes
-keep class io.flutter.embedding.engine.deferredcomponents.** { *; }

# Keep specific Flutter plugins (more targeted approach)
-keep class io.flutter.plugins.googlesignin.** { *; }
-keep class io.flutter.plugins.connectivity.** { *; }
-keep class io.flutter.plugins.sharedpreferences.** { *; }
-keep class io.flutter.plugins.imagepicker.** { *; }
-keep class io.flutter.plugins.inapppurchase.** { *; }

# ============================================================================
# CRITICAL SECURITY: REVENUECAT PROTECTION (PREVIOUSLY MISSING!)
# ============================================================================

# Protect RevenueCat subscription validation - CRITICAL for preventing bypass
-keep class com.revenuecat.purchases.** { *; }
-keep class com.revenuecat.purchases.interfaces.** { *; }
-keep class com.revenuecat.purchases.models.** { *; }
-keep class com.revenuecat.purchases.common.** { *; }
-dontwarn com.revenuecat.purchases.**

# ============================================================================
# AUTHENTICATION & SECURITY SERVICES
# ============================================================================

# Keep Supabase authentication classes
-keep class io.github.jan.supabase.** { *; }
-keep class com.supabase.** { *; }

# Google Sign-In protection
-keep class com.google.android.gms.auth.** { *; }
-keep class com.google.android.gms.common.** { *; }
-keep class com.google.android.gms.signin.** { *; }

# ============================================================================
# GOOGLE PLAY SERVICES PROTECTION
# ============================================================================

# Keep Play Core classes
-keep class com.google.android.play.core.** { *; }

# Keep Play Asset Delivery classes
-keep class com.google.android.play.asset.** { *; }

# Keep Play Feature Delivery classes
-keep class com.google.android.play.feature.** { *; }

# Keep Play In-App Reviews classes
-keep class com.google.android.play.review.** { *; }

# Keep Play In-App Updates classes
-keep class com.google.android.play.update.** { *; }
-keep class com.google.android.play.app.update.** { *; }

# ============================================================================
# MEDICAL APP SPECIFIC PROTECTIONS
# ============================================================================

# Keep PDF generation and viewing classes (Syncfusion)
-keep class com.syncfusion.** { *; }

# ============================================================================
# NETWORK SECURITY & ENCRYPTION
# ============================================================================

# Protect SSL/TLS and network security classes
-keep class javax.net.ssl.** { *; }
-keep class org.apache.http.** { *; }

# Keep serialization annotations for API communication
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# ============================================================================
# NATIVE CODE & REFLECTION PROTECTION
# ============================================================================

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep custom exceptions for proper error handling
-keep public class * extends java.lang.Exception

# Keep classes with reflection usage
-keepclassmembers class * {
    @androidx.annotation.Keep *;
}

# ============================================================================
# WARNING SUPPRESSIONS
# ============================================================================

# Suppress warnings for missing Play Core classes
-dontwarn com.google.android.play.core.tasks.OnFailureListener
-dontwarn com.google.android.play.core.tasks.OnSuccessListener
-dontwarn com.google.android.play.core.tasks.Task

# Suppress warnings for optional dependencies
-dontwarn org.apache.http.**
-dontwarn javax.net.ssl.**
