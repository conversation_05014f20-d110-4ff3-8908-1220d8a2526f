import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile_model.dart';

class UserProfileService {
  static const String _profileKey = 'user_profile';
  static const String _profileSlotPrefix = 'user_profile_slot_';
  static const String _currentSlotKey = 'current_profile_slot';

  static final Map<int, UserProfile> _cachedProfileSlots = {};
  static int? _currentSlot;

  static Future<UserProfile> getUserProfile() async {
    // Get the current active profile slot
    final currentSlot = await getCurrentProfileSlot();
    return await getUserProfileFromSlot(currentSlot);
  }

  static Future<bool> saveUserProfile(UserProfile profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = jsonEncode(profile.toJson());

      final success = await prefs.setString(_profileKey, profileJson);

      if (success) {
        debugPrint('Profile saved successfully: ${profile.toJson()}');
      }

      return success;
    } catch (e) {
      debugPrint('Error saving user profile: $e');
      return false;
    }
  }

  static void clearCachedProfile() {
    _cachedProfileSlots.clear();
    debugPrint('Cached profile cleared');
  }

  // Profile Slot Management Methods
  static Future<UserProfile> getUserProfileFromSlot(int slotNumber) async {
    if (slotNumber < 1 || slotNumber > 5) {
      throw ArgumentError('Slot number must be between 1 and 5');
    }

    if (_cachedProfileSlots.containsKey(slotNumber)) {
      return _cachedProfileSlots[slotNumber]!;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final profileKey = '$_profileSlotPrefix$slotNumber';
      final profileJson = prefs.getString(profileKey);

      if (profileJson != null) {
        final profileData = jsonDecode(profileJson) as Map<String, dynamic>;
        final profile = UserProfile.fromJson(profileData);
        _cachedProfileSlots[slotNumber] = profile;
        return profile;
      }

      // Return default profile if slot is empty
      final defaultProfile = UserProfile.defaultProfile;
      _cachedProfileSlots[slotNumber] = defaultProfile;
      return defaultProfile;
    } catch (e) {
      debugPrint('Error getting user profile from slot $slotNumber: $e');
      final defaultProfile = UserProfile.defaultProfile;
      _cachedProfileSlots[slotNumber] = defaultProfile;
      return defaultProfile;
    }
  }

  static Future<bool> saveUserProfileToSlot(
    UserProfile profile,
    int slotNumber,
  ) async {
    if (slotNumber < 1 || slotNumber > 5) {
      throw ArgumentError('Slot number must be between 1 and 5');
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final profileKey = '$_profileSlotPrefix$slotNumber';
      final profileJson = jsonEncode(profile.toJson());

      final success = await prefs.setString(profileKey, profileJson);

      if (success) {
        _cachedProfileSlots[slotNumber] = profile;
        debugPrint(
          'Profile saved successfully to slot $slotNumber: ${profile.toJson()}',
        );
      }

      return success;
    } catch (e) {
      debugPrint('Error saving user profile to slot $slotNumber: $e');
      return false;
    }
  }

  static Future<bool> hasProfileInSlot(int slotNumber) async {
    if (slotNumber < 1 || slotNumber > 5) {
      return false;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final profileKey = '$_profileSlotPrefix$slotNumber';
      return prefs.containsKey(profileKey);
    } catch (e) {
      debugPrint('Error checking profile slot $slotNumber: $e');
      return false;
    }
  }

  static Future<List<bool>> getProfileSlotsStatus() async {
    List<bool> slotsStatus = [];
    for (int i = 1; i <= 5; i++) {
      slotsStatus.add(await hasProfileInSlot(i));
    }
    return slotsStatus;
  }

  static void clearCachedProfileSlots() {
    _cachedProfileSlots.clear();
    debugPrint('Cached profile slots cleared');
  }

  // Current Profile Slot Management
  static Future<int> getCurrentProfileSlot() async {
    if (_currentSlot != null) {
      return _currentSlot!;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final currentSlot = prefs.getInt(_currentSlotKey) ?? 1;
      _currentSlot = currentSlot;
      return currentSlot;
    } catch (e) {
      debugPrint('Error getting current profile slot: $e');
      _currentSlot = 1;
      return 1;
    }
  }

  static Future<bool> setCurrentProfileSlot(int slotNumber) async {
    if (slotNumber < 1 || slotNumber > 5) {
      throw ArgumentError('Slot number must be between 1 and 5');
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final success = await prefs.setInt(_currentSlotKey, slotNumber);

      if (success) {
        _currentSlot = slotNumber;
        // Clear cached profiles to force reload
        _cachedProfileSlots.clear();
        debugPrint('Current profile slot set to: $slotNumber');
      }

      return success;
    } catch (e) {
      debugPrint('Error setting current profile slot: $e');
      return false;
    }
  }

  static Future<String?> saveLogoImage(File imageFile) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imageName =
          'custom_logo_${DateTime.now().millisecondsSinceEpoch}.png';
      final imagePath = '${directory.path}/$imageName';

      await imageFile.copy(imagePath);

      return imagePath;
    } catch (e) {
      debugPrint('Error saving logo image: $e');
      return null;
    }
  }

  static Future<File?> getLogoImageFile() async {
    try {
      final profile = await getUserProfile();

      if (profile.logoImagePath != null) {
        final file = File(profile.logoImagePath!);
        if (await file.exists()) {
          return file;
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting logo image: $e');
      return null;
    }
  }

  static Future<String?> saveBackgroundImage(File imageFile) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imageName =
          'background_${DateTime.now().millisecondsSinceEpoch}.png';
      final imagePath = '${directory.path}/$imageName';

      await imageFile.copy(imagePath);

      return imagePath;
    } catch (e) {
      debugPrint('Error saving background image: $e');
      return null;
    }
  }

  static Future<String?> savePositionedBackgroundImage(
    File imageFile,
    String position,
  ) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final imageName =
          'background_${position}_${DateTime.now().millisecondsSinceEpoch}.png';
      final imagePath = '${directory.path}/$imageName';

      await imageFile.copy(imagePath);

      return imagePath;
    } catch (e) {
      debugPrint('Error saving positioned background image: $e');
      return null;
    }
  }

  static Future<File?> getBackgroundImageFileByPath(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        return file;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting background image by path: $e');
      return null;
    }
  }

  static Future<File?> getBackgroundImageFile() async {
    try {
      final profile = await getUserProfile();

      if (profile.backgroundImagePath != null) {
        final file = File(profile.backgroundImagePath!);
        if (await file.exists()) {
          return file;
        }
      }

      return null;
    } catch (e) {
      debugPrint('Error getting background image: $e');
      return null;
    }
  }
}
