class BackgroundImage {
  final String path;
  final String position;
  final String type;
  final String color;
  final double alpha;

  BackgroundImage({
    required this.path,
    required this.position,
    this.type = 'image',
    this.color = '000000',
    this.alpha = 1.0,
  });

  Map<String, dynamic> toJson() {
    return {
      'path': path,
      'position': position,
      'type': type,
      'color': color,
      'alpha': alpha,
    };
  }

  factory BackgroundImage.fromJson(Map<String, dynamic> json) {
    return BackgroundImage(
      path: json['path'] ?? '',
      position: json['position'] ?? 'center',
      type: json['type'] ?? 'image',
      color: json['color'] ?? '000000',
      alpha: json['alpha']?.toDouble() ?? 1.0,
    );
  }
}

class UserProfile {
  final String languageCode;

  final String? logoImagePath;

  final String? backgroundImagePath;

  final List<BackgroundImage> backgroundImages;

  final bool useArabicHeader;

  final bool useArabicLeftHeader;

  final String headerText;

  final String doctorNameEn;

  final String doctorNameAr;

  final String doctorDegreeEn;

  final String doctorDegreeAr;

  final String doctorLocationEn;

  final String doctorLocationAr;

  final String doctorSpecializationEn;

  final String doctorSpecializationAr;

  // Right header Arabic fields (separate from left header)
  final String rightDoctorNameAr;

  final String rightDoctorDegreeAr;

  final String rightDoctorLocationAr;

  final String rightDoctorSpecializationAr;

  final String reportTitleColor;
  final String doctorTitleColor;
  final String patientInfoTextColor;
  final String echoParametersTextColor;
  final String interpretationTextColor;
  final String conclusionTextColor;
  final String footerTextColor;

  final double pdfBackgroundTransparency;

  UserProfile({
    this.languageCode = 'en',
    this.logoImagePath,
    this.backgroundImagePath,
    this.backgroundImages = const [],
    this.useArabicHeader = false,
    this.useArabicLeftHeader = false,
    this.headerText = '',
    this.doctorNameEn = 'Doctor',
    this.doctorNameAr = '',
    this.doctorDegreeEn = '',
    this.doctorDegreeAr = '',
    this.doctorLocationEn = '',
    this.doctorLocationAr = '',
    this.doctorSpecializationEn = '',
    this.doctorSpecializationAr = '',
    this.rightDoctorNameAr = '',
    this.rightDoctorDegreeAr = '',
    this.rightDoctorLocationAr = '',
    this.rightDoctorSpecializationAr = '',
    this.reportTitleColor = '000000',
    this.doctorTitleColor = '000000',
    this.patientInfoTextColor = '000000',
    this.echoParametersTextColor = '000000',
    this.interpretationTextColor = '000000',
    this.conclusionTextColor = '000000',
    this.footerTextColor = '000000',
    this.pdfBackgroundTransparency = 1.0,
  });

  UserProfile copyWith({
    String? languageCode,
    String? logoImagePath,
    String? backgroundImagePath,
    List<BackgroundImage>? backgroundImages,
    bool? useArabicHeader,
    bool? useArabicLeftHeader,
    String? headerText,
    String? doctorNameEn,
    String? doctorNameAr,
    String? doctorDegreeEn,
    String? doctorDegreeAr,
    String? doctorLocationEn,
    String? doctorLocationAr,
    String? doctorSpecializationEn,
    String? doctorSpecializationAr,
    String? rightDoctorNameAr,
    String? rightDoctorDegreeAr,
    String? rightDoctorLocationAr,
    String? rightDoctorSpecializationAr,
    String? reportTitleColor,
    String? doctorTitleColor,
    String? patientInfoTextColor,
    String? echoParametersTextColor,
    String? interpretationTextColor,
    String? conclusionTextColor,
    String? footerTextColor,
    double? pdfBackgroundTransparency,
  }) {
    return UserProfile(
      languageCode: languageCode ?? this.languageCode,
      logoImagePath: logoImagePath ?? this.logoImagePath,
      backgroundImagePath: backgroundImagePath ?? this.backgroundImagePath,
      backgroundImages: backgroundImages ?? this.backgroundImages,
      useArabicHeader: useArabicHeader ?? this.useArabicHeader,
      useArabicLeftHeader: useArabicLeftHeader ?? this.useArabicLeftHeader,
      headerText: headerText ?? this.headerText,
      doctorNameEn: doctorNameEn ?? this.doctorNameEn,
      doctorNameAr: doctorNameAr ?? this.doctorNameAr,
      doctorDegreeEn: doctorDegreeEn ?? this.doctorDegreeEn,
      doctorDegreeAr: doctorDegreeAr ?? this.doctorDegreeAr,
      doctorLocationEn: doctorLocationEn ?? this.doctorLocationEn,
      doctorLocationAr: doctorLocationAr ?? this.doctorLocationAr,
      doctorSpecializationEn:
          doctorSpecializationEn ?? this.doctorSpecializationEn,
      doctorSpecializationAr:
          doctorSpecializationAr ?? this.doctorSpecializationAr,
      rightDoctorNameAr: rightDoctorNameAr ?? this.rightDoctorNameAr,
      rightDoctorDegreeAr: rightDoctorDegreeAr ?? this.rightDoctorDegreeAr,
      rightDoctorLocationAr:
          rightDoctorLocationAr ?? this.rightDoctorLocationAr,
      rightDoctorSpecializationAr:
          rightDoctorSpecializationAr ?? this.rightDoctorSpecializationAr,
      reportTitleColor: reportTitleColor ?? this.reportTitleColor,
      doctorTitleColor: doctorTitleColor ?? this.doctorTitleColor,
      patientInfoTextColor: patientInfoTextColor ?? this.patientInfoTextColor,
      echoParametersTextColor:
          echoParametersTextColor ?? this.echoParametersTextColor,
      interpretationTextColor:
          interpretationTextColor ?? this.interpretationTextColor,
      conclusionTextColor: conclusionTextColor ?? this.conclusionTextColor,
      footerTextColor: footerTextColor ?? this.footerTextColor,
      pdfBackgroundTransparency:
          pdfBackgroundTransparency ?? this.pdfBackgroundTransparency,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'languageCode': languageCode,
      'logoImagePath': logoImagePath,
      'backgroundImagePath': backgroundImagePath,
      'backgroundImages': backgroundImages.map((img) => img.toJson()).toList(),
      'useArabicHeader': useArabicHeader,
      'useArabicLeftHeader': useArabicLeftHeader,
      'headerText': headerText,
      'doctorNameEn': doctorNameEn,
      'doctorNameAr': doctorNameAr,
      'doctorDegreeEn': doctorDegreeEn,
      'doctorDegreeAr': doctorDegreeAr,
      'doctorLocationEn': doctorLocationEn,
      'doctorLocationAr': doctorLocationAr,
      'doctorSpecializationEn': doctorSpecializationEn,
      'doctorSpecializationAr': doctorSpecializationAr,
      'rightDoctorNameAr': rightDoctorNameAr,
      'rightDoctorDegreeAr': rightDoctorDegreeAr,
      'rightDoctorLocationAr': rightDoctorLocationAr,
      'rightDoctorSpecializationAr': rightDoctorSpecializationAr,
      'reportTitleColor': reportTitleColor,
      'doctorTitleColor': doctorTitleColor,
      'patientInfoTextColor': patientInfoTextColor,
      'echoParametersTextColor': echoParametersTextColor,
      'interpretationTextColor': interpretationTextColor,
      'conclusionTextColor': conclusionTextColor,
      'footerTextColor': footerTextColor,
      'pdfBackgroundTransparency': pdfBackgroundTransparency,
    };
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    List<BackgroundImage> bgImages = [];
    if (json['backgroundImages'] != null) {
      final List<dynamic> bgImagesList = json['backgroundImages'];
      bgImages =
          bgImagesList
              .map((imgJson) => BackgroundImage.fromJson(imgJson))
              .toList();
    }

    return UserProfile(
      languageCode: json['languageCode'] ?? 'en',
      logoImagePath: json['logoImagePath'],
      backgroundImagePath: json['backgroundImagePath'],
      backgroundImages: bgImages,
      useArabicHeader: json['useArabicHeader'] ?? false,
      useArabicLeftHeader: json['useArabicLeftHeader'] ?? false,
      headerText: json['headerText'] ?? '',
      doctorNameEn: json['doctorNameEn'] ?? 'Doctor',
      doctorNameAr: json['doctorNameAr'] ?? '',
      doctorDegreeEn: json['doctorDegreeEn'] ?? '',
      doctorDegreeAr: json['doctorDegreeAr'] ?? '',
      doctorLocationEn: json['doctorLocationEn'] ?? '',
      doctorLocationAr: json['doctorLocationAr'] ?? '',
      doctorSpecializationEn: json['doctorSpecializationEn'] ?? '',
      doctorSpecializationAr: json['doctorSpecializationAr'] ?? '',
      rightDoctorNameAr: json['rightDoctorNameAr'] ?? '',
      rightDoctorDegreeAr: json['rightDoctorDegreeAr'] ?? '',
      rightDoctorLocationAr: json['rightDoctorLocationAr'] ?? '',
      rightDoctorSpecializationAr: json['rightDoctorSpecializationAr'] ?? '',

      reportTitleColor:
          json['reportTitleColor'] ?? json['headerTextColor'] ?? '000000',
      doctorTitleColor: json['doctorTitleColor'] ?? '000000',
      patientInfoTextColor: json['patientInfoTextColor'] ?? '000000',
      echoParametersTextColor: json['echoParametersTextColor'] ?? '000000',
      interpretationTextColor: json['interpretationTextColor'] ?? '000000',
      conclusionTextColor: json['conclusionTextColor'] ?? '000000',
      footerTextColor: json['footerTextColor'] ?? '000000',
      pdfBackgroundTransparency:
          (json['pdfBackgroundTransparency'] as num?)?.toDouble() ?? 1.0,
    );
  }

  static UserProfile get defaultProfile => UserProfile();
}
