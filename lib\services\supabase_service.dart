import 'package:flutter/material.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';
import '../utils/connectivity_helper.dart';

class SupabaseService {
  static final supabase = Supabase.instance.client;

  static const String redirectUrl = 'com.syvursoft.atria://auth-callback';

  static User? get currentUser => supabase.auth.currentUser;

  static bool get isSignedIn => currentUser != null;

  static Future<bool> verifySession() async {
    try {
      if (!isSignedIn) return false;

      // Check connectivity before making network call
      await ConnectivityHelper.ensureConnection();

      final user = await supabase.auth.getUser();
      return user.user != null;
    } catch (e) {
      // If it's a connectivity error, don't sign out
      if (e.toString().contains('No internet connection')) {
        rethrow;
      }
      await supabase.auth.signOut();
      return false;
    }
  }

  static Future<void> signOut() async {
    await supabase.auth.signOut();
  }

  static Future<void> resetPasswordForEmail(
    String email, {
    String? redirectTo,
  }) async {
    try {
      // Check connectivity before making network call
      await ConnectivityHelper.ensureConnection();

      await supabase.auth.resetPasswordForEmail(email, redirectTo: redirectTo);
    } catch (e) {
      rethrow;
    }
  }

  static Future<void> updatePassword(String newPassword) async {
    try {
      // Check connectivity before making network call
      await ConnectivityHelper.ensureConnection();

      await supabase.auth.updateUser(UserAttributes(password: newPassword));
    } catch (e) {
      rethrow;
    }
  }

  static Future<void> updatePasswordWithToken(
    String newPassword,
    String accessToken,
  ) async {
    try {
      final currentSession = supabase.auth.currentSession;
      final currentUser = supabase.auth.currentUser;

      if (currentSession == null || currentUser == null) {
        try {
          final response = await supabase.auth.setSession(accessToken);
          if (response.session == null) {
            throw Exception(
              'Failed to authenticate with the provided token. The link may have expired.',
            );
          }
        } catch (e) {
          try {
            final response = await supabase.auth.recoverSession(accessToken);
            if (response.session == null) {
              throw Exception(
                'Failed to authenticate. Please request a new password reset link.',
              );
            }
          } catch (recoverError) {
            throw Exception(
              'Failed to authenticate. The password reset link may have expired.',
            );
          }
        }
      }

      final response = await supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      if (response.user == null) {
        throw Exception('Failed to update password. Please try again.');
      }
    } catch (e) {
      rethrow;
    }
  }

  static Future<bool> handleDeepLink(Uri uri) async {
    try {
      if (uri.toString().contains('auth-callback')) {
        final hash = uri.fragment;
        if (hash.isNotEmpty) {
          final params = Uri.splitQueryString(hash);
          final accessToken = params['access_token'];
          final refreshToken = params['refresh_token'];

          if (accessToken != null && refreshToken != null) {
            final response = await supabase.auth.recoverSession(accessToken);
            return response.session != null;
          }
        }

        final type = uri.queryParameters['type'];
        if (type == 'recovery') {
          try {
            final token = uri.queryParameters['token'];
            if (token != null) {
              if (isSignedIn) {
                await signOut();
              }

              await supabase.auth.setSession(token);
            }
          } catch (e) {}

          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  static Widget getResetPasswordWidget({
    required void Function(UserResponse response) onSuccess,
    required void Function(Object error) onError,
  }) {
    return SupaResetPassword(
      accessToken: supabase.auth.currentSession?.accessToken,
      onSuccess: onSuccess,
      onError: onError,
    );
  }
}
