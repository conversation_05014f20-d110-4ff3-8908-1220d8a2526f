name: atria
description: "A new Flutter project."
publish_to: 'none'
version: 9.0.0

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  font_awesome_flutter: ^10.6.0
  permission_handler: ^12.0.0+1

  # PDF generation and viewing
  printing: ^5.11.1
  syncfusion_flutter_pdfviewer: ^30.1.37
  pdfrx: ^1.0.0

  # OCR and image processing
  google_mlkit_text_recognition: ^0.15.0

  # Rich text editing
  flutter_quill: ^11.2.0


  # Medical-specific tools
  syncfusion_flutter_pdf: ^30.1.37
  syncfusion_flutter_core: ^30.1.37


  # Image handling
  image_picker: ^1.1.2
  image: ^4.0.17

  # File handling
  file_picker: ^10.2.0


  # Additional utilities
  path_provider: ^2.1.5
  intl: ^0.20.2
  shared_preferences: ^2.2.2
  flutter_launcher_icons: ^0.14.3

  # Supabase
  supabase_flutter: ^2.9.0
  supabase_auth_ui: ^0.5.5
  app_links: ^6.4.0






  # Custom packages
  supabase_auth_ui_custom:
    path: packages/supabase_auth_ui_custom
  connectivity_plus: ^6.1.4
  flutter_local_notifications: ^19.2.1
  flutter_secure_storage: ^9.2.4
  url_launcher: ^6.3.1
  flutter_colorpicker: ^1.1.0
  http: ^1.2.1





dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  msix: ^3.16.8
  # Temporary dependency for migration utility

flutter:
  uses-material-design: true

  assets:
    - lib/assets/images/
    - lib/assets/fonts/
    - lib/assets/images/appicon.png

  fonts:

    - family: NotoNaskhArabic
      fonts:
        - asset: lib/assets/fonts/NotoNaskhArabic-Medium.ttf

# MSIX Configuration
msix_config:
  display_name: Atria
  publisher_display_name: Syvursoft
  identity_name: com.syvursoft.atria
  msix_version: *******
  description: Advanced documentation
  publisher: CN=Syvursoft, O=Syvursoft, C=US
  logo_path: lib/assets/images/appicon.png
  start_menu_icon_path: lib/assets/images/appicon.png
  tile_icon_path: lib/assets/images/appicon.png
  icons_background_color: transparent
  architecture: x64
  capabilities: 'internetClient,microphone,webcam,documentsLibrary,picturesLibrary'
  signing_certificate: auto
  certificate_subject: CN=Syvursoft, O=Syvursoft, C=US
  certificate_password: ''
  install_certificate: true
