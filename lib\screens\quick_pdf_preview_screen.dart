import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:printing/printing.dart';
import '../services/quick_pdf_service.dart';
import '../models/interpretation_section.dart';
import '../models/report_model.dart';
import '../services/report_storage_service.dart';
import 'pdf_editor_screen.dart';

class QuickPdfPreviewScreen extends StatefulWidget {
  final Map<String, dynamic>? patientData;
  final List<InterpretationSection> interpretationSections;

  const QuickPdfPreviewScreen({
    super.key,
    this.patientData,
    required this.interpretationSections,
  });

  @override
  State<QuickPdfPreviewScreen> createState() => _QuickPdfPreviewScreenState();
}

class _QuickPdfPreviewScreenState extends State<QuickPdfPreviewScreen> {
  bool _isLoading = true;
  bool _isSaving = false;
  Uint8List? _pdfBytes;
  final GlobalKey<SfPdfViewerState> _pdfViewerKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  Future<void> _loadPdf() async {
    try {
      final bytes = await QuickPdfService.generateQuickReport(
        patientData: widget.patientData,
        interpretationSections: widget.interpretationSections,
      );

      setState(() {
        _pdfBytes = bytes;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error loading PDF: $e')));
      }
    }
  }

  Future<void> _printPdf() async {
    if (_pdfBytes == null) return;

    await Printing.layoutPdf(
      onLayout: (format) => _pdfBytes!,
      name: 'ATRIA Quick Report',
    );
  }

  Future<void> _sharePdf() async {
    if (_pdfBytes == null) return;

    final result = await Printing.sharePdf(
      bytes: _pdfBytes!,
      filename: 'atria_quick_report.pdf',
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result ? 'PDF shared successfully' : 'Failed to share PDF',
          ),
        ),
      );
    }
  }

  Future<void> _editPdf() async {
    if (_pdfBytes == null || widget.patientData == null) return;

    // Create a temporary report for editing
    final tempReport = Report(
      id: 'temp_${DateTime.now().millisecondsSinceEpoch}',
      title:
          widget.patientData!['patientInfo']['name'] as String? ??
          'Quick Report',
      createdAt: DateTime.now(),
      patientName:
          widget.patientData!['patientInfo']['name'] as String? ??
          'Unknown Patient',
      reportType: 'quick',
      patientData: widget.patientData,
      pdfBytes: _pdfBytes,
    );

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PdfEditorScreen(report: tempReport),
      ),
    );

    if (result != null && result is Report) {
      // Update the current PDF with the edited version
      setState(() {
        _pdfBytes = result.pdfBytes;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('PDF updated successfully')),
        );
      }
    }
  }

  Future<void> _saveReport() async {
    if (_pdfBytes == null || widget.patientData == null) return;

    setState(() {
      _isSaving = true;
    });

    try {
      final patientName =
          widget.patientData!['patientInfo']['name'] as String? ??
          'Unknown Patient';

      final id = DateTime.now().millisecondsSinceEpoch.toString();

      final title = patientName;

      final report = Report(
        id: id,
        title: title,
        createdAt: DateTime.now(),
        patientName: patientName,
        reportType: 'quick',
        patientData: widget.patientData,
        pdfBytes: _pdfBytes,
      );

      final success = await ReportStorageService.saveReport(report);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success ? 'Report saved successfully' : 'Failed to save report',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error saving report: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quick Report Preview'),
        backgroundColor: Colors.orange.shade800,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              _loadPdf();
            },
          ),
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'Edit',
            onPressed: _pdfBytes == null ? null : _editPdf,
          ),
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'Print',
            onPressed: _pdfBytes == null ? null : _printPdf,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            tooltip: 'Share',
            onPressed: _pdfBytes == null ? null : _sharePdf,
          ),
          IconButton(
            icon: const Icon(Icons.save),
            tooltip: 'Save Report',
            onPressed: _pdfBytes == null || _isSaving ? null : _saveReport,
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: Colors.orange.shade800),
                    const SizedBox(height: 16),
                    const Text('Generating Quick Report...'),
                  ],
                ),
              )
              : _pdfBytes != null
              ? SfPdfViewer.memory(
                _pdfBytes!,
                key: _pdfViewerKey,
                canShowScrollHead: true,
                canShowScrollStatus: true,
                enableDoubleTapZooming: true,
                enableTextSelection: true,
                interactionMode: PdfInteractionMode.pan,
                pageLayoutMode: PdfPageLayoutMode.single,
              )
              : Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 48, color: Colors.red),
                    const SizedBox(height: 16),
                    const Text('Failed to load PDF'),
                  ],
                ),
              ),
    );
  }
}
