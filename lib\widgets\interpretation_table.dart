import 'package:flutter/material.dart';

class InterpretationTable extends StatelessWidget {
  final List<Map<String, String>> interpretationData;

  const InterpretationTable({super.key, required this.interpretationData});

  @override
  Widget build(BuildContext context) {
    return DataTable(
      columns: const <DataColumn>[
        DataColumn(
          label: Text('Section', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        DataColumn(
          label: Text(
            'Interpretation',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
        ),
      ],
      rows:
          interpretationData
              .map(
                (item) => DataRow(
                  cells: <DataCell>[
                    DataCell(Text(item['section']!)),
                    DataCell(Text(item['interpretation']!)),
                  ],
                ),
              )
              .toList(),
    );
  }
}
