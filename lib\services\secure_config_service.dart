import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';

class SecureConfigService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
    lOptions: LinuxOptions(),
    wOptions: WindowsOptions(useBackwardCompatibility: false),
    mOptions: MacOsOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static const String _supabaseUrlKey = 'SUPABASE_URL';
  static const String _supabaseAnonKeyKey = 'SUPABASE_ANON_KEY';
  static const String _revenueCatApiKeyKey = 'REVENUECAT_API_KEY';

  static Future<void> initialize() async {
    try {
      final supabaseUrl = await _storage.read(key: _supabaseUrlKey);
      final supabaseAnonKey = await _storage.read(key: _supabaseAnonKeyKey);
      final revenueCatApiKey = await _storage.read(key: _revenueCatApiKeyKey);

      if (supabaseUrl == null ||
          supabaseAnonKey == null ||
          revenueCatApiKey == null) {
        throw Exception(
          'Missing required configuration values in secure storage. '
          'Please run the migration utility first.',
        );
      }

      if (kDebugMode) {
        print(
          'SecureConfigService: All configuration values loaded successfully',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('SecureConfigService initialization error: $e');
      }
      rethrow;
    }
  }

  static Future<String> getSupabaseUrl() async {
    final value = await _storage.read(key: _supabaseUrlKey);
    if (value == null) {
      throw Exception('Supabase URL not found in secure storage');
    }
    return value;
  }

  static Future<String> getSupabaseAnonKey() async {
    final value = await _storage.read(key: _supabaseAnonKeyKey);
    if (value == null) {
      throw Exception('Supabase Anonymous Key not found in secure storage');
    }
    return value;
  }

  static Future<void> storeSupabaseUrl(String value) async {
    await _storage.write(key: _supabaseUrlKey, value: value);
  }

  static Future<void> storeSupabaseAnonKey(String value) async {
    await _storage.write(key: _supabaseAnonKeyKey, value: value);
  }

  static Future<String> getRevenueCatApiKey() async {
    final value = await _storage.read(key: _revenueCatApiKeyKey);
    if (value == null) {
      throw Exception('RevenueCat API Key not found in secure storage');
    }
    return value;
  }

  static Future<void> storeRevenueCatApiKey(String value) async {
    await _storage.write(key: _revenueCatApiKeyKey, value: value);
  }

  static Future<void> clearAll() async {
    await _storage.delete(key: _supabaseUrlKey);
    await _storage.delete(key: _supabaseAnonKeyKey);
    await _storage.delete(key: _revenueCatApiKeyKey);
  }

  static Future<bool> hasConfiguration() async {
    final supabaseUrl = await _storage.read(key: _supabaseUrlKey);
    final supabaseAnonKey = await _storage.read(key: _supabaseAnonKeyKey);
    final revenueCatApiKey = await _storage.read(key: _revenueCatApiKeyKey);

    return supabaseUrl != null &&
        supabaseAnonKey != null &&
        revenueCatApiKey != null;
  }
}
