import 'base_interpretation.dart';

class OtherFindingsInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'No unusual findings are noted.';
    }

    final unusualFindings = data['unusualFindings'] ?? {};
    final septalDeformity =
        unusualFindings['septalDeformity'] as String? ?? 'None';
    final finding = unusualFindings['finding'] as String? ?? 'None';
    final findingLocation = unusualFindings['location'] as String? ?? '';
    final description = unusualFindings['description'] as String? ?? '';

    final StringBuffer otherFindings = StringBuffer();

    if (septalDeformity.toLowerCase() == 'none') {
    } else {
      String lowerCaseDeformity = septalDeformity.toLowerCase();

      if (lowerCaseDeformity == 'hypertrophy') {
        otherFindings.write(
          'There is septal hypertrophy with increased wall thickness',
        );
      } else if (lowerCaseDeformity == 'flattening') {
        otherFindings.write(
          'There is septal flattening, which may indicate right ventricular volume or pressure overload',
        );
      } else if (lowerCaseDeformity == 'bowing') {
        otherFindings.write(
          'There is septal bowing toward the left ventricle, suggesting right ventricular pressure overload',
        );
      } else if (lowerCaseDeformity == 'aneurysm') {
        otherFindings.write(
          'There is a septal aneurysm with abnormal bulging of the interventricular septum',
        );
      } else if (lowerCaseDeformity == 'vsd') {
        otherFindings.write(
          'There is a ventricular septal defect (VSD) with abnormal communication between the left and right ventricles',
        );
      } else if (lowerCaseDeformity == 'righttoleftshunt') {
        otherFindings.write(
          'There is evidence of a right-to-left shunt across the interventricular septum, which may indicate elevated right-sided pressures',
        );
      } else {
        otherFindings.write('There is $septalDeformity septal deformity');
      }
    }

    if (finding.toLowerCase() != 'none') {
      if (otherFindings.isNotEmpty) {
        otherFindings.write('. ');
      }

      String detailedFinding = getDetailedFindingDescription(
        finding,
        findingLocation,
        description,
      );
      otherFindings.write(detailedFinding);
    }

    if (septalDeformity.toLowerCase() == 'none' &&
        finding.toLowerCase() == 'none') {
      otherFindings.write(
        'The interventricular septum is normal. No unusual findings are noted',
      );
    }

    if (!otherFindings.toString().endsWith('.')) {
      otherFindings.write('.');
    }

    return otherFindings.toString();
  }

  String getDetailedFindingDescription(
    String finding,
    String location,
    String description,
  ) {
    final StringBuffer detailedDescription = StringBuffer();

    String lowerCaseFinding = finding.toLowerCase();

    if (lowerCaseFinding == 'mass') {
      detailedDescription.write('There is a mass identified in the $location');
      if (description.isNotEmpty) {
        detailedDescription.write('. The mass appears to be $description');
      } else {
        detailedDescription.write(
          '. Further evaluation with additional imaging modalities may be warranted',
        );
      }
    } else if (lowerCaseFinding == 'thrombus') {
      detailedDescription.write(
        'There is a thrombus identified in the $location',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. The thrombus is $description');
      } else {
        detailedDescription.write(
          '. Anticoagulation therapy should be considered',
        );
      }
    } else if (lowerCaseFinding == 'vegetation') {
      detailedDescription.write(
        'There is a vegetation identified on the $location',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. The vegetation is $description');
      } else {
        detailedDescription.write(
          '. This finding is consistent with infective endocarditis',
        );
      }
    } else if (lowerCaseFinding == 'rupture') {
      detailedDescription.write(
        'There is evidence of rupture in the $location',
      );
      if (description.isNotEmpty) {
        detailedDescription.write(
          '. The rupture is characterized by $description',
        );
      } else {
        detailedDescription.write(
          '. This is a critical finding requiring immediate attention',
        );
      }
    } else if (lowerCaseFinding == 'pseudoaneurysm') {
      detailedDescription.write(
        'There is a pseudoaneurysm identified in the $location',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. The pseudoaneurysm is $description');
      } else {
        detailedDescription.write(
          '. This finding may require surgical evaluation',
        );
      }
    } else if (lowerCaseFinding == 'dissection') {
      detailedDescription.write('There is evidence of aortic dissection');
      if (location.isNotEmpty) {
        detailedDescription.write(' in the $location');
      }
      if (description.isNotEmpty) {
        detailedDescription.write(
          '. The dissection is characterized by $description',
        );
      } else {
        detailedDescription.write(
          '. This is a critical finding requiring immediate attention',
        );
      }
    } else if (lowerCaseFinding == 'flap') {
      detailedDescription.write('There is an intimal flap identified');
      if (location.isNotEmpty) {
        detailedDescription.write(' in the $location');
      }
      if (description.isNotEmpty) {
        detailedDescription.write('. The flap is $description');
      } else {
        detailedDescription.write(
          '. This finding may be consistent with aortic dissection',
        );
      }
    } else if (lowerCaseFinding == 'intimaltear') {
      detailedDescription.write('There is an intimal tear identified');
      if (location.isNotEmpty) {
        detailedDescription.write(' in the $location');
      }
      if (description.isNotEmpty) {
        detailedDescription.write('. The tear is $description');
      } else {
        detailedDescription.write(
          '. This finding may be consistent with aortic dissection',
        );
      }
    } else if (lowerCaseFinding == 'apicalballooning') {
      detailedDescription.write(
        'There is apical ballooning of the left ventricle',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          '. This finding is consistent with Takotsubo cardiomyopathy (stress-induced cardiomyopathy)',
        );
      }
    } else if (lowerCaseFinding == 'takotsubo') {
      detailedDescription.write(
        'There are findings consistent with Takotsubo cardiomyopathy',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          ', including apical ballooning with hyperkinesis of the basal segments',
        );
      }
    } else if (lowerCaseFinding == 'cardiactamponade') {
      detailedDescription.write('There is evidence of cardiac tamponade');
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          ' with right atrial and right ventricular diastolic collapse. This is a critical finding requiring immediate intervention',
        );
      }
    } else if (lowerCaseFinding == 'restrictivephysiology') {
      detailedDescription.write('There is evidence of restrictive physiology');
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          ' with preserved systolic function, severe diastolic dysfunction, and biatrial enlargement',
        );
      }
    } else if (lowerCaseFinding == 'constrictivepericarditis') {
      detailedDescription.write(
        'There are findings consistent with constrictive pericarditis',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          ' including septal bounce, respiratory variation in ventricular filling, and pericardial thickening',
        );
      }
    } else if (lowerCaseFinding == 'hypertrophicobstruction') {
      detailedDescription.write(
        'There is evidence of hypertrophic obstructive cardiomyopathy',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          ' with asymmetric septal hypertrophy and left ventricular outflow tract obstruction',
        );
      }
    } else if (lowerCaseFinding == 'cardiacamyloidosis') {
      detailedDescription.write(
        'There are findings consistent with cardiac amyloidosis',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          ' including increased ventricular wall thickness with a sparkling appearance, biatrial enlargement, and restrictive filling pattern',
        );
      }
    } else if (lowerCaseFinding == 'cardiacsarcoidosis') {
      detailedDescription.write(
        'There are findings suggestive of cardiac sarcoidosis',
      );
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          ' including regional wall motion abnormalities, ventricular aneurysms, and thinning of the basal interventricular septum',
        );
      }
    } else if (lowerCaseFinding == 'cardiactumor') {
      detailedDescription.write('There is a cardiac tumor identified');
      if (location.isNotEmpty) {
        detailedDescription.write(' in the $location');
      }
      if (description.isNotEmpty) {
        detailedDescription.write('. The tumor is $description');
      } else {
        detailedDescription.write(
          '. Further evaluation with additional imaging modalities is recommended',
        );
      }
    } else if (lowerCaseFinding == 'cardiacmetastasis') {
      detailedDescription.write('There is evidence of cardiac metastasis');
      if (location.isNotEmpty) {
        detailedDescription.write(' in the $location');
      }
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          '. This finding suggests malignant involvement of the heart',
        );
      }
    } else if (lowerCaseFinding == 'pulmonaryembolism') {
      detailedDescription.write('There is evidence of pulmonary embolism');
      if (location.isNotEmpty) {
        detailedDescription.write(' in the $location');
      }
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          ' with right ventricular strain. This is a critical finding requiring immediate attention',
        );
      }
    } else if (lowerCaseFinding == 'infectiveendocarditis') {
      detailedDescription.write('There is evidence of infective endocarditis');
      if (location.isNotEmpty) {
        detailedDescription.write(' with vegetation on the $location');
      }
      if (description.isNotEmpty) {
        detailedDescription.write('. $description');
      } else {
        detailedDescription.write(
          '. Blood cultures and antibiotic therapy should be considered. This is a serious finding requiring prompt medical attention',
        );
      }
    } else if (lowerCaseFinding == 'other') {
      if (location.isNotEmpty && description.isNotEmpty) {
        detailedDescription.write(
          'There is an unusual finding in the $location: $description',
        );
      } else if (location.isNotEmpty) {
        detailedDescription.write(
          'There is an unusual finding in the $location',
        );
      } else if (description.isNotEmpty) {
        detailedDescription.write('There is an unusual finding: $description');
      } else {
        detailedDescription.write(
          'There is an unusual finding that requires further evaluation',
        );
      }
    } else {
      if (location.isNotEmpty && description.isNotEmpty) {
        detailedDescription.write(
          'There is $finding found in $location: $description',
        );
      } else if (location.isNotEmpty) {
        detailedDescription.write('There is $finding found in $location');
      } else {
        detailedDescription.write('There is $finding noted');
      }
    }

    return detailedDescription.toString();
  }
}
