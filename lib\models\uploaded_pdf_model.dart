import 'dart:typed_data';

class UploadedPdf {
  final String id;
  final String name;
  final String filePath;
  final DateTime uploadDate;
  final int fileSize;
  final Uint8List? pdfBytes;

  UploadedPdf({
    required this.id,
    required this.name,
    required this.filePath,
    required this.uploadDate,
    required this.fileSize,
    this.pdfBytes,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'filePath': filePath,
      'uploadDate': uploadDate.toIso8601String(),
      'fileSize': fileSize,
    };
  }

  factory UploadedPdf.fromJson(Map<String, dynamic> json) {
    return UploadedPdf(
      id: json['id'],
      name: json['name'],
      filePath: json['filePath'],
      uploadDate: DateTime.parse(json['uploadDate']),
      fileSize: json['fileSize'],
    );
  }

  UploadedPdf copyWith({
    String? id,
    String? name,
    String? filePath,
    DateTime? uploadDate,
    int? fileSize,
    Uint8List? pdfBytes,
  }) {
    return UploadedPdf(
      id: id ?? this.id,
      name: name ?? this.name,
      filePath: filePath ?? this.filePath,
      uploadDate: uploadDate ?? this.uploadDate,
      fileSize: fileSize ?? this.fileSize,
      pdfBytes: pdfBytes ?? this.pdfBytes,
    );
  }

  String get formattedFileSize {
    if (fileSize < 1024) {
      return '$fileSize B';
    } else if (fileSize < 1024 * 1024) {
      return '${(fileSize / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}
