import 'dart:typed_data';

class Report {
  final String id;
  final String title;
  final DateTime createdAt;
  final String patientName;
  final String reportType;
  final Map<String, dynamic>? patientData;
  final Uint8List? pdfBytes;

  Report({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.patientName,
    required this.reportType,
    this.patientData,
    this.pdfBytes,
  });

  factory Report.fromJson(Map<String, dynamic> json) {
    return Report(
      id: json['id'],
      title: json['title'],
      createdAt: DateTime.parse(json['createdAt']),
      patientName: json['patientName'],
      reportType: json['reportType'],
      patientData: json['patientData'],
      pdfBytes:
          json['pdfBytes'] != null
              ? Uint8List.fromList(List<int>.from(json['pdfBytes']))
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'patientName': patientName,
      'reportType': reportType,
      'patientData': patientData,
      'pdfBytes': pdfBytes?.toList(),
    };
  }
}
