import 'dart:math' as math;

class BSACalculator {
  static double calculateBSA(double height, double weight) {
    if (height <= 0 || weight <= 0) return 0;
    return math.sqrt(height * weight / 3600);
  }

  static double calculateBSAFromString(String heightStr, String weightStr) {
    try {
      final double height = double.parse(heightStr);
      final double weight = double.parse(weightStr);
      return calculateBSA(height, weight);
    } catch (e) {
      return 0;
    }
  }

  static double getDefaultBSA(String gender) {
    return gender.toLowerCase() == 'male' ? 1.9 : 1.6;
  }
}
