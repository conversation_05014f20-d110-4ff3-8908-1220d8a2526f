import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/uploaded_pdf_model.dart';

class PdfStorageService {
  static const String _pdfListKey = 'uploaded_pdfs_list';
  static const String _pdfFolderName = 'uploaded_pdfs';

  /// Get the directory where PDFs are stored
  static Future<Directory> _getPdfDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final pdfDir = Directory('${appDir.path}/$_pdfFolderName');

    if (!await pdfDir.exists()) {
      await pdfDir.create(recursive: true);
    }

    return pdfDir;
  }

  /// Save a PDF file and return the UploadedPdf model
  static Future<UploadedPdf?> savePdf({
    required String fileName,
    required Uint8List pdfBytes,
  }) async {
    try {
      debugPrint('🔄 PdfStorageService: Starting PDF save process');
      debugPrint('📄 Original filename: $fileName');
      debugPrint('📊 PDF bytes length: ${pdfBytes.length}');

      final pdfDir = await _getPdfDirectory();
      debugPrint('📁 PDF directory: ${pdfDir.path}');

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final id = 'pdf_$timestamp';
      debugPrint('🆔 Generated ID: $id');

      // Clean the filename and ensure it has .pdf extension
      String cleanFileName = fileName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');
      if (!cleanFileName.toLowerCase().endsWith('.pdf')) {
        cleanFileName += '.pdf';
      }
      debugPrint('🧹 Clean filename: $cleanFileName');

      final filePath = '${pdfDir.path}/${id}_$cleanFileName';
      debugPrint('🔗 Full file path: $filePath');

      final file = File(filePath);

      // Write the PDF bytes to file
      debugPrint('💾 Writing PDF bytes to file...');
      await file.writeAsBytes(pdfBytes);

      // Verify file was written
      final fileExists = await file.exists();
      final fileSize = await file.length();
      debugPrint(
        '✅ File written successfully: exists=$fileExists, size=$fileSize',
      );

      final uploadedPdf = UploadedPdf(
        id: id,
        name: cleanFileName,
        filePath: filePath,
        uploadDate: DateTime.now(),
        fileSize: pdfBytes.length,
        pdfBytes: pdfBytes,
      );

      // Save to preferences list
      debugPrint('💾 Adding PDF to preferences list...');
      await _addPdfToList(uploadedPdf);

      debugPrint('✅ PDF saved successfully: $cleanFileName');
      return uploadedPdf;
    } catch (e, stackTrace) {
      debugPrint('❌ Error saving PDF: $e');
      debugPrint('Stack trace: $stackTrace');
      return null;
    }
  }

  /// Get all uploaded PDFs
  static Future<List<UploadedPdf>> getAllPdfs() async {
    try {
      debugPrint('🔄 PdfStorageService: Loading all PDFs');
      final prefs = await SharedPreferences.getInstance();
      final pdfListJson = prefs.getStringList(_pdfListKey) ?? [];
      debugPrint('📋 Found ${pdfListJson.length} PDF entries in preferences');

      final pdfs = <UploadedPdf>[];

      for (final jsonString in pdfListJson) {
        try {
          final json = jsonDecode(jsonString);
          final pdf = UploadedPdf.fromJson(json);
          debugPrint('📄 Processing PDF: ${pdf.name}');

          // Check if file still exists
          final file = File(pdf.filePath);
          final fileExists = await file.exists();
          debugPrint('📁 File exists: $fileExists for ${pdf.filePath}');

          if (fileExists) {
            pdfs.add(pdf);
            debugPrint('✅ Added PDF to list: ${pdf.name}');
          } else {
            debugPrint('🗑️ Removing missing PDF from list: ${pdf.name}');
            // Remove from list if file doesn't exist
            await _removePdfFromList(pdf.id);
          }
        } catch (e) {
          debugPrint('❌ Error parsing PDF JSON: $e');
        }
      }

      // Sort by upload date (newest first)
      pdfs.sort((a, b) => b.uploadDate.compareTo(a.uploadDate));

      debugPrint('📊 Returning ${pdfs.length} valid PDFs');
      return pdfs;
    } catch (e) {
      debugPrint('❌ Error getting PDFs: $e');
      return [];
    }
  }

  /// Get PDF bytes for a specific PDF
  static Future<Uint8List?> getPdfBytes(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsBytes();
      }
      return null;
    } catch (e) {
      debugPrint('Error reading PDF bytes: $e');
      return null;
    }
  }

  /// Delete a PDF file and remove from list
  static Future<bool> deletePdf(String pdfId) async {
    try {
      final pdfs = await getAllPdfs();
      final pdf = pdfs.firstWhere((p) => p.id == pdfId);

      // Delete the file
      final file = File(pdf.filePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Remove from preferences list
      await _removePdfFromList(pdfId);

      debugPrint('✅ PDF deleted successfully: ${pdf.name}');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting PDF: $e');
      return false;
    }
  }

  /// Rename a PDF
  static Future<UploadedPdf?> renamePdf(String pdfId, String newName) async {
    try {
      final pdfs = await getAllPdfs();
      final pdf = pdfs.firstWhere((p) => p.id == pdfId);

      // Clean the new filename
      String cleanNewName = newName.replaceAll(RegExp(r'[<>:"/\\|?*]'), '_');
      if (!cleanNewName.toLowerCase().endsWith('.pdf')) {
        cleanNewName += '.pdf';
      }

      final pdfDir = await _getPdfDirectory();
      final newFilePath = '${pdfDir.path}/${pdf.id}_$cleanNewName';

      // Rename the file
      final oldFile = File(pdf.filePath);
      final newFile = await oldFile.rename(newFilePath);

      // Update the PDF model
      final updatedPdf = pdf.copyWith(
        name: cleanNewName,
        filePath: newFile.path,
      );

      // Update in preferences
      await _removePdfFromList(pdfId);
      await _addPdfToList(updatedPdf);

      debugPrint('✅ PDF renamed successfully: $cleanNewName');
      return updatedPdf;
    } catch (e) {
      debugPrint('❌ Error renaming PDF: $e');
      return null;
    }
  }

  /// Add PDF to the preferences list
  static Future<void> _addPdfToList(UploadedPdf pdf) async {
    final prefs = await SharedPreferences.getInstance();
    final pdfListJson = prefs.getStringList(_pdfListKey) ?? [];

    pdfListJson.add(jsonEncode(pdf.toJson()));
    await prefs.setStringList(_pdfListKey, pdfListJson);
  }

  /// Remove PDF from the preferences list
  static Future<void> _removePdfFromList(String pdfId) async {
    final prefs = await SharedPreferences.getInstance();
    final pdfListJson = prefs.getStringList(_pdfListKey) ?? [];

    pdfListJson.removeWhere((jsonString) {
      try {
        final json = jsonDecode(jsonString);
        return json['id'] == pdfId;
      } catch (e) {
        return false;
      }
    });

    await prefs.setStringList(_pdfListKey, pdfListJson);
  }

  /// Clear all PDFs (for testing/cleanup)
  static Future<void> clearAllPdfs() async {
    try {
      final pdfDir = await _getPdfDirectory();
      if (await pdfDir.exists()) {
        await pdfDir.delete(recursive: true);
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_pdfListKey);

      debugPrint('✅ All PDFs cleared');
    } catch (e) {
      debugPrint('❌ Error clearing PDFs: $e');
    }
  }
}
