import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/smart_template_model.dart';

class SmartTemplateService {
  static const String _templatesKey = 'smart_templates';

  static Future<List<SmartTemplate>> getTemplates() async {
    final prefs = await SharedPreferences.getInstance();
    final templatesJson = prefs.getStringList(_templatesKey) ?? [];

    return templatesJson.map((templateJson) {
      final Map<String, dynamic> templateMap = jsonDecode(templateJson);
      return SmartTemplate.fromJson(templateMap);
    }).toList();
  }

  static Future<void> saveTemplate(SmartTemplate template) async {
    final templates = await getTemplates();

    templates.removeWhere((t) => t.id == template.id);

    templates.add(template);

    templates.sort((a, b) => b.lastUsed.compareTo(a.lastUsed));

    await _saveTemplates(templates);
  }

  static Future<void> deleteTemplate(String templateId) async {
    final templates = await getTemplates();
    templates.removeWhere((t) => t.id == templateId);
    await _saveTemplates(templates);
  }

  static Future<void> updateLastUsed(String templateId) async {
    final templates = await getTemplates();
    final templateIndex = templates.indexWhere((t) => t.id == templateId);

    if (templateIndex != -1) {
      final updatedTemplate = templates[templateIndex].copyWith(
        lastUsed: DateTime.now(),
      );
      templates[templateIndex] = updatedTemplate;

      templates.sort((a, b) => b.lastUsed.compareTo(a.lastUsed));

      await _saveTemplates(templates);
    }
  }

  static Future<SmartTemplate?> getTemplate(String templateId) async {
    final templates = await getTemplates();
    try {
      return templates.firstWhere((t) => t.id == templateId);
    } catch (e) {
      return null;
    }
  }

  static Future<void> _saveTemplates(List<SmartTemplate> templates) async {
    final prefs = await SharedPreferences.getInstance();
    final templatesJson =
        templates.map((template) {
          return jsonEncode(template.toJson());
        }).toList();

    await prefs.setStringList(_templatesKey, templatesJson);
  }

  static Future<SmartTemplate> createTemplateFromQuickReportData(
    String name,
    String description,
    Map<String, dynamic> quickReportData,
  ) async {
    final template = SmartTemplate(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description,
      createdAt: DateTime.now(),
      lastUsed: DateTime.now(),
      templateData: quickReportData,
    );

    await saveTemplate(template);
    return template;
  }

  static Future<void> clearAllTemplates() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_templatesKey);
  }

  static Future<void> createDefaultNormalTemplates() async {
    final maleData = {
      'patientInfo': {
        'name': '',
        'gender': 'male',
        'age': 45,
        'priority': 'routine',
        'indication': 'Routine screening',
        'location': 'outpatient',
        'requestedBy': null,
        'height': 175.0,
        'weight': 75.0,
      },
      'echoParameters': {
        'lvedd': 50,
        'lvesd': 32,
        'ivsd': 09,
        'lvpw': 09,
        'relativeWallThickness': 0.35,
        'la': 38,
        'ao': 32,
        'ef': 60,
        'fs': 35,
        'diastolicGrade': 'No',
        'tapse': 20,
        'pasp': 25,
        'rvSize': 'normal',
        'raSize': 'normal',
      },
      'wallMotion': {
        'basalAnterior': 'normal',
        'basalAnteroseptal': 'normal',
        'basalInferoseptal': 'normal',
        'basalInferior': 'normal',
        'basalInferolateral': 'normal',
        'basalAnterolateral': 'normal',
        'midAnterior': 'normal',
        'midAnteroseptal': 'normal',
        'midInferoseptal': 'normal',
        'midInferior': 'normal',
        'midInferolateral': 'normal',
        'midAnterolateral': 'normal',
        'apicalAnterior': 'normal',
        'apicalSeptal': 'normal',
        'apicalInferior': 'normal',
        'apicalLateral': 'normal',
        'apex': 'normal',
      },
      'valves': {
        'morphology': {
          'Mitral': 'normal',
          'Aortic': 'normal',
          'Tricuspid': 'normal',
          'Pulmonary': 'normal',
        },
        'stenosis': {
          'Mitral': 'none',
          'Aortic': 'none',
          'Tricuspid': 'none',
          'Pulmonary': 'none',
        },
        'regurgitation': {
          'Mitral': 'trivial',
          'Aortic': 'trivial',
          'Tricuspid': 'trivial',
          'Pulmonary': 'trivial',
        },
      },
      'pericardium': {
        'effusionSeverity': 'none',
        'effusionLocation': 'circumferential',
      },
      'ivc': {'diameter': '2.0', 'collapsibility': 'normal'},
      'unusualFindings': {
        'septalDeformity': 'none',
        'finding': 'none',
        'location': '',
        'description': '',
      },
    };

    final femaleData = {
      'patientInfo': {
        'name': '',
        'gender': 'female',
        'age': 45,
        'priority': 'routine',
        'indication': 'Routine screening',
        'location': 'outpatient',
        'requestedBy': null,
        'height': 165.0,
        'weight': 65.0,
      },
      'echoParameters': {
        'lvedd': 45,
        'lvesd': 28,
        'ivsd': 08,
        'lvpw': 08,
        'relativeWallThickness': 0.32,
        'la': 35,
        'ao': 29,
        'ef': 62,
        'fs': 36,
        'diastolicGrade': 'No',
        'tapse': 18,
        'pasp': 22,
        'rvSize': 'normal',
        'raSize': 'normal',
      },
      'wallMotion': {
        'basalAnterior': 'normal',
        'basalAnteroseptal': 'normal',
        'basalInferoseptal': 'normal',
        'basalInferior': 'normal',
        'basalInferolateral': 'normal',
        'basalAnterolateral': 'normal',
        'midAnterior': 'normal',
        'midAnteroseptal': 'normal',
        'midInferoseptal': 'normal',
        'midInferior': 'normal',
        'midInferolateral': 'normal',
        'midAnterolateral': 'normal',
        'apicalAnterior': 'normal',
        'apicalSeptal': 'normal',
        'apicalInferior': 'normal',
        'apicalLateral': 'normal',
        'apex': 'normal',
      },
      'valves': {
        'morphology': {
          'Mitral': 'normal',
          'Aortic': 'normal',
          'Tricuspid': 'normal',
          'Pulmonary': 'normal',
        },
        'stenosis': {
          'Mitral': 'none',
          'Aortic': 'none',
          'Tricuspid': 'none',
          'Pulmonary': 'none',
        },
        'regurgitation': {
          'Mitral': 'trivial',
          'Aortic': 'trivial',
          'Tricuspid': 'trivial',
          'Pulmonary': 'trivial',
        },
      },
      'pericardium': {
        'effusionSeverity': 'none',
        'effusionLocation': 'circumferential',
      },
      'ivc': {'diameter': '1.8', 'collapsibility': 'normal'},
      'unusualFindings': {
        'septalDeformity': 'none',
        'finding': 'none',
        'location': '',
        'description': '',
      },
    };

    await createTemplateFromQuickReportData(
      'Male Routine Normal',
      'Standard normal values for routine adult male patients',
      maleData,
    );

    await createTemplateFromQuickReportData(
      'Female Routine Normal',
      'Standard normal values for routine adult female patients',
      femaleData,
    );
  }
}
