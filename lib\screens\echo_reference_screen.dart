import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:ui';
import 'dart:async';
import '../constants/font_sizes.dart';

class EchoReferenceScreen extends StatefulWidget {
  const EchoReferenceScreen({super.key});

  @override
  State<EchoReferenceScreen> createState() => _EchoReferenceScreenState();
}

class _EchoReferenceScreenState extends State<EchoReferenceScreen> {
  int _selectedTabIndex = 0;

  bool _isSearchVisible = false;
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _searchResults = [];
  final FocusNode _searchFocusNode = FocusNode();
  Timer? _searchDebouncer;

  final Map<String, Widget> _cachedSections = {};
  static const int _maxCacheSize = 50;

  final Map<String, bool> _expandedSections = {
    'dimensions': false,
    'volumes': false,
    'function': false,
    'mass': false,
    'diastolic': false,
    'advancedSystolic': false,
    'strain': false,
    'performance': false,
    'wallMotion': false,

    'laDimensions': false,
    'laFunction': false,
    'laPressure': false,
    'laPulmonaryVeins': false,
    'laAdvanced': false,
    'laStrainRate': false,
    'laAppendage': false,
    'laMechanical': false,
    'laSynchrony': false,
    'laCompliance': false,
    'laPhasic': false,
    'laEjection': false,

    'rvDimensions': false,
    'rvVolumes': false,
    'rvFunction': false,
    'rvPressure': false,
    'rvDiastolic': false,
    'rvPulmonary': false,
    'rvAdvanced': false,
    'rvPACoupling': false,
    'rvStrainRate': false,
    'rvSynchrony': false,
    'rvPulmonaryVascular': false,
    'rvHemodynamics': false,
    'rvEnergetics': false,
    'rvExercise': false,
    'rvOutflow': false,
    'rvTricuspidQuant': false,
    'rvWallMotion': false,

    'raDimensions': false,
    'raFunction': false,
    'raPressure': false,
    'raAdvanced': false,
    'raTricuspid': false,
    'raHepatic': false,
    'raSVC': false,
    'raTricuspidInflow': false,
    'raMechanical': false,
    'raCompliance': false,
    'raEnhancedTricuspid': false,

    'mitralValve': false,
    'aorticValve': false,
    'tricuspidValve': false,
    'pulmonaryValve': false,

    'aorticRoot': false,
    'ascendingAorta': false,
    'aorticArch': false,
    'descendingAorta': false,
    'aorticPathology': false,
  };

  final List<Map<String, dynamic>> _tabs = [
    {
      'title': 'Left Ventricle',
      'shortTitle': 'LV',
      'icon': FontAwesomeIcons.heart,
      'gradient': [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
    },
    {
      'title': 'Left Atrium',
      'shortTitle': 'LA',
      'icon': FontAwesomeIcons.heartCircleCheck,
      'gradient': [Color(0xFFFF9FF3), Color(0xFFFFB3F7)],
    },
    {
      'title': 'Right Ventricle',
      'shortTitle': 'RV',
      'icon': FontAwesomeIcons.heartPulse,
      'gradient': [Color(0xFF4ECDC4), Color(0xFF6EE7E0)],
    },
    {
      'title': 'Right Atrium',
      'shortTitle': 'RA',
      'icon': FontAwesomeIcons.heartCirclePlus,
      'gradient': [Color(0xFF2ECC71), Color(0xFF58D68D)],
    },
    {
      'title': 'Valves',
      'shortTitle': 'VLV',
      'icon': FontAwesomeIcons.heartCircleBolt,
      'gradient': [Color(0xFF9B59B6), Color(0xFFBB7FD1)],
    },
    {
      'title': 'Aorta',
      'shortTitle': 'AO',
      'icon': FontAwesomeIcons.heartCircleExclamation,
      'gradient': [Color(0xFFFF9500), Color(0xFFFFB347)],
    },
  ];

  final List<Map<String, dynamic>> _allParameters = [
    {
      'title': 'LV Internal Diameter at End-Diastole (LVIDd)',
      'tab': 0,
      'section': 'dimensions',
    },
    {
      'title': 'LV Internal Diameter at End-Systole (LVIDs)',
      'tab': 0,
      'section': 'dimensions',
    },
    {
      'title': 'Interventricular Septal Thickness (IVSd)',
      'tab': 0,
      'section': 'dimensions',
    },
    {
      'title': 'Posterior Wall Thickness (PWd)',
      'tab': 0,
      'section': 'dimensions',
    },
    {
      'title': 'LV Diastolic Diameter/Height',
      'tab': 0,
      'section': 'dimensions',
    },
    {
      'title': 'LV End-Diastolic Volume (LVEDV)',
      'tab': 0,
      'section': 'volumes',
    },
    {'title': 'LV End-Systolic Volume (LVESV)', 'tab': 0, 'section': 'volumes'},
    {'title': 'Ejection Fraction (EF)', 'tab': 0, 'section': 'function'},
    {'title': 'Fractional Shortening (FS)', 'tab': 0, 'section': 'function'},
    {'title': 'Midwall Fractional Shortening', 'tab': 0, 'section': 'function'},
    {'title': 'Ejection Fraction (Biplane)', 'tab': 0, 'section': 'function'},
    {'title': 'LV Mass (Linear Method)', 'tab': 0, 'section': 'mass'},
    {'title': 'LV Mass (2D Method)', 'tab': 0, 'section': 'mass'},
    {'title': 'Relative Wall Thickness (RWT)', 'tab': 0, 'section': 'mass'},
    {'title': 'LV Mass/Height', 'tab': 0, 'section': 'mass'},
    {'title': 'LV Mass/Height^2.7', 'tab': 0, 'section': 'mass'},
    {
      'title': 'Diastolic Function Grading (ECGwaves.com)',
      'tab': 0,
      'section': 'diastolic',
    },
    {'title': 'Mitral Inflow E/A Ratio', 'tab': 0, 'section': 'diastolic'},
    {'title': 'Deceleration Time (DT)', 'tab': 0, 'section': 'diastolic'},
    {'title': 'Tissue Doppler e\' velocity', 'tab': 0, 'section': 'diastolic'},
    {'title': 'E/e\' Ratio', 'tab': 0, 'section': 'diastolic'},
    {
      'title': 'Isovolumic Relaxation Time (IVRT)',
      'tab': 0,
      'section': 'diastolic',
    },
    {
      'title': 'Global Longitudinal Strain (GLS)',
      'tab': 0,
      'section': 'advancedSystolic',
    },
    {
      'title': 'S\' (S prime) Tissue Velocity',
      'tab': 0,
      'section': 'advancedSystolic',
    },
    {
      'title': 'MAPSE (Mitral Annular Plane Systolic Excursion)',
      'tab': 0,
      'section': 'advancedSystolic',
    },
    {
      'title': 'dP/dt (Rate of Pressure Rise)',
      'tab': 0,
      'section': 'advancedSystolic',
    },
    {
      'title': 'Global Circumferential Strain (GCS)',
      'tab': 0,
      'section': 'strain',
    },
    {'title': 'Global Radial Strain (GRS)', 'tab': 0, 'section': 'strain'},
    {'title': 'LV Torsion/Twist', 'tab': 0, 'section': 'strain'},
    {
      'title': 'MPI (Myocardial Performance Index)',
      'tab': 0,
      'section': 'performance',
    },
    {
      'title': 'Regional Wall Motion Scoring',
      'tab': 0,
      'section': 'wallMotion',
    },

    {
      'title': 'LA Diameter (Anteroposterior)',
      'tab': 1,
      'section': 'laDimensions',
    },
    {
      'title': 'LA Volume (Biplane Method)',
      'tab': 1,
      'section': 'laDimensions',
    },
    {'title': 'LA Area (4-Chamber View)', 'tab': 1, 'section': 'laDimensions'},
    {'title': 'LA Sphericity Index', 'tab': 1, 'section': 'laDimensions'},
    {
      'title': 'LA Emptying Fraction (Total)',
      'tab': 1,
      'section': 'laFunction',
    },
    {
      'title': 'LA Passive Emptying Fraction',
      'tab': 1,
      'section': 'laFunction',
    },
    {'title': 'LA Active Emptying Fraction', 'tab': 1, 'section': 'laFunction'},
    {'title': 'LA Pressure (E/e\' Method)', 'tab': 1, 'section': 'laPressure'},
    {'title': 'Pulmonary Vein Flow Pattern', 'tab': 1, 'section': 'laPressure'},
    {
      'title': 'Right Upper Pulmonary Vein (RUPV) Diameter',
      'tab': 1,
      'section': 'laPulmonaryVeins',
    },
    {
      'title': 'Right Lower Pulmonary Vein (RLPV) Diameter',
      'tab': 1,
      'section': 'laPulmonaryVeins',
    },
    {
      'title': 'Left Upper Pulmonary Vein (LUPV) Diameter',
      'tab': 1,
      'section': 'laPulmonaryVeins',
    },
    {
      'title': 'Left Lower Pulmonary Vein (LLPV) Diameter',
      'tab': 1,
      'section': 'laPulmonaryVeins',
    },
    {
      'title': 'PV Flow Reversal Duration',
      'tab': 1,
      'section': 'laPulmonaryVeins',
    },
    {'title': 'PV Systolic Fraction', 'tab': 1, 'section': 'laPulmonaryVeins'},
    {'title': 'LA Reservoir Strain', 'tab': 1, 'section': 'laAdvanced'},
    {'title': 'LA Conduit Strain', 'tab': 1, 'section': 'laAdvanced'},
    {'title': 'LA Contractile Strain', 'tab': 1, 'section': 'laAdvanced'},
    {'title': 'LA Stiffness Index', 'tab': 1, 'section': 'laAdvanced'},
    {
      'title': 'Peak LA Reservoir Strain Rate (SRs)',
      'tab': 1,
      'section': 'laStrainRate',
    },
    {
      'title': 'Peak LA Conduit Strain Rate (SRe)',
      'tab': 1,
      'section': 'laStrainRate',
    },
    {
      'title': 'Peak LA Contractile Strain Rate (SRa)',
      'tab': 1,
      'section': 'laStrainRate',
    },
    {
      'title': 'LA Strain Rate Ratio (SRs/SRa)',
      'tab': 1,
      'section': 'laStrainRate',
    },
    {'title': 'LAA Emptying Velocity', 'tab': 1, 'section': 'laAppendage'},
    {'title': 'LAA Filling Velocity', 'tab': 1, 'section': 'laAppendage'},
    {'title': 'LAA Volume', 'tab': 1, 'section': 'laAppendage'},
    {
      'title': 'LAA Morphology Classification',
      'tab': 1,
      'section': 'laAppendage',
    },
    {'title': 'LAA Orifice Diameter', 'tab': 1, 'section': 'laAppendage'},
    {'title': 'LAA Depth', 'tab': 1, 'section': 'laAppendage'},
    {'title': 'LA Mechanical Dispersion', 'tab': 1, 'section': 'laMechanical'},
    {'title': 'Time to Peak LA Strain', 'tab': 1, 'section': 'laMechanical'},
    {
      'title': 'Intra-LA Dyssynchrony Index',
      'tab': 1,
      'section': 'laSynchrony',
    },
    {'title': 'LA-LV Coupling Index', 'tab': 1, 'section': 'laSynchrony'},
    {
      'title': 'LA Electrical Activation Time',
      'tab': 1,
      'section': 'laSynchrony',
    },
    {'title': 'LA Conduction Velocity', 'tab': 1, 'section': 'laSynchrony'},
    {'title': 'LA Synchrony Score', 'tab': 1, 'section': 'laSynchrony'},
    {
      'title': 'Inter-Atrial Conduction Time',
      'tab': 1,
      'section': 'laSynchrony',
    },
    {'title': 'LA Compliance', 'tab': 1, 'section': 'laCompliance'},
    {
      'title': 'LA Pressure-Volume Relationship',
      'tab': 1,
      'section': 'laCompliance',
    },
    {'title': 'LA Maximum Volume (LAVmax)', 'tab': 1, 'section': 'laPhasic'},
    {'title': 'LA Minimum Volume (LAVmin)', 'tab': 1, 'section': 'laPhasic'},
    {'title': 'LA Pre-A Volume (LAVpreA)', 'tab': 1, 'section': 'laPhasic'},
    {'title': 'LA Work Index', 'tab': 1, 'section': 'laEjection'},
    {'title': 'LA Ejection Force', 'tab': 1, 'section': 'laEjection'},

    {'title': 'RV Basal Diameter', 'tab': 2, 'section': 'rvDimensions'},
    {'title': 'RV Mid-Cavity Diameter', 'tab': 2, 'section': 'rvDimensions'},
    {'title': 'RV Length (4-Chamber)', 'tab': 2, 'section': 'rvDimensions'},
    {'title': 'RV Wall Thickness', 'tab': 2, 'section': 'rvDimensions'},
    {
      'title': 'RV End-Diastolic Volume (RVEDV)',
      'tab': 2,
      'section': 'rvVolumes',
    },
    {
      'title': 'RV End-Systolic Volume (RVESV)',
      'tab': 2,
      'section': 'rvVolumes',
    },
    {'title': 'RV Ejection Fraction (RVEF)', 'tab': 2, 'section': 'rvFunction'},
    {
      'title': 'RV Fractional Area Change (FAC)',
      'tab': 2,
      'section': 'rvFunction',
    },
    {
      'title': 'TAPSE (Tricuspid Annular Plane Systolic Excursion)',
      'tab': 2,
      'section': 'rvFunction',
    },
    {
      'title': 'RV S\' (Tricuspid Lateral S prime)',
      'tab': 2,
      'section': 'rvFunction',
    },
    {'title': 'RV Systolic Pressure (RVSP)', 'tab': 2, 'section': 'rvPressure'},
    {
      'title': 'Pulmonary Artery Systolic Pressure (PASP)',
      'tab': 2,
      'section': 'rvPressure',
    },
    {
      'title': 'Tricuspid Regurgitation Velocity (TR Vmax)',
      'tab': 2,
      'section': 'rvPressure',
    },
    {'title': 'RV dP/dt', 'tab': 2, 'section': 'rvPressure'},
    {'title': 'RV E/A Ratio', 'tab': 2, 'section': 'rvDiastolic'},
    {'title': 'RV E/e\' Ratio', 'tab': 2, 'section': 'rvDiastolic'},
    {'title': 'RV Deceleration Time', 'tab': 2, 'section': 'rvDiastolic'},
    {
      'title': 'Pulmonary Artery Acceleration Time (PAAT)',
      'tab': 2,
      'section': 'rvPulmonary',
    },
    {
      'title': 'Pulmonary Vascular Resistance (PVR)',
      'tab': 2,
      'section': 'rvPulmonary',
    },
    {
      'title': 'RV Global Longitudinal Strain',
      'tab': 2,
      'section': 'rvAdvanced',
    },
    {'title': 'RV Free Wall Strain', 'tab': 2, 'section': 'rvAdvanced'},
    {
      'title': 'RV Myocardial Performance Index',
      'tab': 2,
      'section': 'rvAdvanced',
    },
    {'title': 'TAPSE/PASP Ratio', 'tab': 2, 'section': 'rvPACoupling'},
    {
      'title': 'RV Stroke Volume/End-Systolic Volume',
      'tab': 2,
      'section': 'rvPACoupling',
    },
    {
      'title': 'RV Arterial Elastance (Ea)',
      'tab': 2,
      'section': 'rvPACoupling',
    },
    {'title': 'RV Free Wall Strain Rate', 'tab': 2, 'section': 'rvStrainRate'},
    {'title': 'RV Septal Strain Rate', 'tab': 2, 'section': 'rvStrainRate'},
    {'title': 'RV Global Strain Rate', 'tab': 2, 'section': 'rvStrainRate'},
    {
      'title': 'RV Isovolumic Relaxation Time (IVRT)',
      'tab': 2,
      'section': 'rvDiastolic',
    },
    {
      'title': 'RV Filling Pressure Estimation',
      'tab': 2,
      'section': 'rvDiastolic',
    },
    {'title': 'RV Mechanical Dyssynchrony', 'tab': 2, 'section': 'rvSynchrony'},
    {'title': 'RV-LV Interaction Index', 'tab': 2, 'section': 'rvSynchrony'},
    {
      'title': 'RV Electrical-Mechanical Coupling',
      'tab': 2,
      'section': 'rvSynchrony',
    },
    {
      'title': 'Pulmonary Artery Compliance',
      'tab': 2,
      'section': 'rvPulmonaryVascular',
    },
    {
      'title': 'Pulmonary Artery Pulsatility Index',
      'tab': 2,
      'section': 'rvPulmonaryVascular',
    },
    {
      'title': 'PA Systolic/Diastolic Ratio',
      'tab': 2,
      'section': 'rvPulmonaryVascular',
    },
    {
      'title': 'Pulmonary Artery Mean Pressure (PAMP)',
      'tab': 2,
      'section': 'rvHemodynamics',
    },
    {
      'title': 'Pulmonary Artery Diastolic Pressure (PADP)',
      'tab': 2,
      'section': 'rvHemodynamics',
    },
    {
      'title': 'Transpulmonary Gradient (TPG)',
      'tab': 2,
      'section': 'rvHemodynamics',
    },
    {
      'title': 'Pulmonary Arterial Compliance',
      'tab': 2,
      'section': 'rvHemodynamics',
    },
    {'title': 'RV Stroke Work', 'tab': 2, 'section': 'rvEnergetics'},
    {'title': 'RV Power Output', 'tab': 2, 'section': 'rvEnergetics'},
    {'title': 'RV Efficiency', 'tab': 2, 'section': 'rvEnergetics'},
    {'title': 'RV Mechanical Work Index', 'tab': 2, 'section': 'rvEnergetics'},
    {'title': 'RV Reserve Function', 'tab': 2, 'section': 'rvExercise'},
    {'title': 'Exercise PASP', 'tab': 2, 'section': 'rvExercise'},
    {'title': 'RV Contractile Reserve', 'tab': 2, 'section': 'rvExercise'},
    {'title': 'RVOT Proximal Diameter', 'tab': 2, 'section': 'rvOutflow'},
    {'title': 'RVOT Mid Diameter', 'tab': 2, 'section': 'rvOutflow'},
    {'title': 'RVOT Distal Diameter', 'tab': 2, 'section': 'rvOutflow'},
    {'title': 'RVOT Acceleration Time', 'tab': 2, 'section': 'rvOutflow'},
    {
      'title': 'RVOT Ejection Pattern Analysis',
      'tab': 2,
      'section': 'rvOutflow',
    },
    {
      'title': 'TR Effective Regurgitant Orifice Area (EROA)',
      'tab': 2,
      'section': 'rvTricuspidQuant',
    },
    {'title': 'TR Regurgitant Volume', 'tab': 2, 'section': 'rvTricuspidQuant'},
    {
      'title': 'TR Regurgitant Fraction',
      'tab': 2,
      'section': 'rvTricuspidQuant',
    },
    {
      'title': 'TR Vena Contracta Width',
      'tab': 2,
      'section': 'rvTricuspidQuant',
    },
    {'title': 'RV Regional Wall Motion', 'tab': 2, 'section': 'rvWallMotion'},
    {
      'title': 'RV Wall Motion Score Index',
      'tab': 2,
      'section': 'rvWallMotion',
    },
    {
      'title': 'RV Apical vs Basal Function',
      'tab': 2,
      'section': 'rvWallMotion',
    },
    {
      'title': 'RV Free Wall vs Septal Function',
      'tab': 2,
      'section': 'rvWallMotion',
    },

    {'title': 'RA Area (4-Chamber)', 'tab': 3, 'section': 'raDimensions'},
    {'title': 'RA Volume (Biplane)', 'tab': 3, 'section': 'raDimensions'},
    {'title': 'RA Major Axis Length', 'tab': 3, 'section': 'raDimensions'},
    {'title': 'RA Minor Axis Length', 'tab': 3, 'section': 'raDimensions'},
    {
      'title': 'RA Emptying Fraction (Total)',
      'tab': 3,
      'section': 'raFunction',
    },
    {
      'title': 'RA Passive Emptying Fraction',
      'tab': 3,
      'section': 'raFunction',
    },
    {'title': 'RA Active Emptying Fraction', 'tab': 3, 'section': 'raFunction'},
    {'title': 'RA Pressure (Estimated)', 'tab': 3, 'section': 'raPressure'},
    {'title': 'IVC Diameter', 'tab': 3, 'section': 'raPressure'},
    {'title': 'IVC Collapsibility Index', 'tab': 3, 'section': 'raPressure'},
    {'title': 'RA Reservoir Strain', 'tab': 3, 'section': 'raAdvanced'},
    {'title': 'RA Conduit Strain', 'tab': 3, 'section': 'raAdvanced'},
    {'title': 'RA Contractile Strain', 'tab': 3, 'section': 'raAdvanced'},
    {
      'title': 'Tricuspid Inflow E/A Ratio',
      'tab': 3,
      'section': 'raTricuspidInflow',
    },
    {
      'title': 'Tricuspid E/e\' Ratio',
      'tab': 3,
      'section': 'raTricuspidInflow',
    },
    {
      'title': 'Tricuspid Deceleration Time',
      'tab': 3,
      'section': 'raTricuspidInflow',
    },
    {'title': 'Hepatic Vein Flow Pattern', 'tab': 3, 'section': 'raHepatic'},
    {'title': 'Hepatic Vein S/D Ratio', 'tab': 3, 'section': 'raHepatic'},
    {'title': 'SVC Flow Pattern', 'tab': 3, 'section': 'raSVC'},
    {'title': 'SVC Peak Velocity', 'tab': 3, 'section': 'raSVC'},
    {'title': 'RA Mechanical Dispersion', 'tab': 3, 'section': 'raMechanical'},
    {'title': 'Time to Peak RA Strain', 'tab': 3, 'section': 'raMechanical'},
    {'title': 'RA Compliance', 'tab': 3, 'section': 'raCompliance'},
    {
      'title': 'RA Pressure-Volume Relationship',
      'tab': 3,
      'section': 'raCompliance',
    },

    {'title': 'Mitral Valve Area (MVA)', 'tab': 4, 'section': 'mitralValve'},
    {'title': 'Mitral Mean Gradient', 'tab': 4, 'section': 'mitralValve'},
    {'title': 'Mitral Peak Gradient', 'tab': 4, 'section': 'mitralValve'},
    {'title': 'PISA Radius for MR', 'tab': 4, 'section': 'mitralValve'},
    {'title': 'Aortic Valve Area (AVA)', 'tab': 4, 'section': 'aorticValve'},
    {'title': 'Aortic Mean Gradient', 'tab': 4, 'section': 'aorticValve'},
    {'title': 'Aortic Peak Velocity', 'tab': 4, 'section': 'aorticValve'},
    {'title': 'Dimensionless Index (DI)', 'tab': 4, 'section': 'aorticValve'},
    {'title': 'Tricuspid Valve Area', 'tab': 4, 'section': 'tricuspidValve'},
    {'title': 'Tricuspid Mean Gradient', 'tab': 4, 'section': 'tricuspidValve'},
    {'title': 'Tricuspid Peak Velocity', 'tab': 4, 'section': 'tricuspidValve'},
    {'title': 'Pulmonary Valve Area', 'tab': 4, 'section': 'pulmonaryValve'},
    {'title': 'Pulmonary Peak Velocity', 'tab': 4, 'section': 'pulmonaryValve'},
    {'title': 'Pulmonary Mean Gradient', 'tab': 4, 'section': 'pulmonaryValve'},

    {
      'title': 'Aortic Root Diameter (Sinus of Valsalva)',
      'tab': 5,
      'section': 'aorticRoot',
    },
    {'title': 'Aortic Annulus Diameter', 'tab': 5, 'section': 'aorticRoot'},
    {
      'title': 'Sinotubular Junction Diameter',
      'tab': 5,
      'section': 'aorticRoot',
    },
    {
      'title': 'Ascending Aorta Diameter (Mid-Level)',
      'tab': 5,
      'section': 'ascendingAorta',
    },
    {
      'title': 'Ascending Aorta Peak Velocity',
      'tab': 5,
      'section': 'ascendingAorta',
    },
    {'title': 'Aortic Arch Diameter', 'tab': 5, 'section': 'aorticArch'},
    {
      'title': 'Descending Aorta Diameter',
      'tab': 5,
      'section': 'descendingAorta',
    },
    {
      'title': 'Descending Aorta Peak Velocity',
      'tab': 5,
      'section': 'descendingAorta',
    },
    {
      'title': 'Diastolic Flow Reversal (AR Assessment)',
      'tab': 5,
      'section': 'descendingAorta',
    },
    {
      'title': 'Aortic Regurgitation Jet Assessment',
      'tab': 5,
      'section': 'aorticPathology',
    },
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    _searchDebouncer?.cancel();
    super.dispose();
  }

  void _performSearch(String query) {
    _searchDebouncer?.cancel();

    _searchDebouncer = Timer(const Duration(milliseconds: 300), () {
      if (query.isEmpty) {
        setState(() {
          _searchResults.clear();
        });
        return;
      }

      final results =
          _allParameters.where((parameter) {
            return parameter['title'].toString().toLowerCase().contains(
              query.toLowerCase(),
            );
          }).toList();

      setState(() {
        _searchResults = results;
      });
    });
  }

  void _navigateToParameter(Map<String, dynamic> parameter) {
    final tabIndex = parameter['tab'] as int;
    final sectionKey = parameter['section'] as String;

    _searchFocusNode.unfocus();

    setState(() {
      _isSearchVisible = false;
      _searchController.clear();
      _searchResults.clear();
    });

    Future.delayed(const Duration(milliseconds: 100), () {
      setState(() {
        _selectedTabIndex = tabIndex;
        _expandedSections[sectionKey] = true;
      });
    });
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (!_isSearchVisible) {
        _searchController.clear();
        _searchResults.clear();
      }
    });

    if (_isSearchVisible) {
      Future.delayed(const Duration(milliseconds: 100), () {
        _searchFocusNode.requestFocus();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title:
            _isSearchVisible
                ? TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: FontSizes.bodyLarge,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Search parameters...',
                    hintStyle: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                    border: InputBorder.none,
                    suffixIcon: IconButton(
                      icon: Icon(Icons.clear, color: Colors.white),
                      onPressed: () {
                        _searchController.clear();
                        _performSearch('');
                      },
                    ),
                  ),
                  onChanged: _performSearch,
                )
                : Text(
                  'Echo Reference',
                  style: TextStyle(
                    fontSize: FontSizes.heading2,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _isSearchVisible ? Icons.close : Icons.search,
              color: Colors.white,
            ),
            onPressed: _toggleSearch,
          ),
        ],
        flexibleSpace: Container(
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.95),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(color: Color(0xFF667eea)),
        child: SafeArea(
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SizedBox(
                height: constraints.maxHeight,
                child:
                    _isSearchVisible && _searchResults.isNotEmpty
                        ? _buildSearchResults()
                        : _buildTabContent(),
              );
            },
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _cachedSections.clear();
          setState(() {});
        },
        backgroundColor: _tabs[_selectedTabIndex]['gradient'][0],
        child: Icon(Icons.refresh, color: Colors.white),
      ),
    );
  }

  Widget _buildGlassmorphicContainer({
    required Widget child,
    String? cacheKey,
    bool useBlur = false,
  }) {
    if (cacheKey != null && _cachedSections.containsKey(cacheKey)) {
      return _cachedSections[cacheKey]!;
    }

    Widget container;

    if (useBlur) {
      container = ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: child,
          ),
        ),
      );
    } else {
      container = Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.15),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 2,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: child,
      );
    }

    if (cacheKey != null) {
      if (_cachedSections.length >= _maxCacheSize) {
        final keysToRemove =
            _cachedSections.keys.take(_maxCacheSize ~/ 2).toList();
        for (final key in keysToRemove) {
          _cachedSections.remove(key);
        }
      }
      _cachedSections[cacheKey] = container;
    }

    return container;
  }

  Widget _buildBottomNavigation() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.9),
        border: Border(
          top: BorderSide(color: Colors.white.withValues(alpha: 0.2), width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 2,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(_tabs.length, (index) {
          final tab = _tabs[index];
          final isSelected = index == _selectedTabIndex;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedTabIndex = index;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? tab['gradient'][0].withValues(alpha: 0.8)
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(20),
                boxShadow:
                    isSelected
                        ? [
                          BoxShadow(
                            color: tab['gradient'][0].withValues(alpha: 0.3),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ]
                        : null,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    tab['icon'],
                    color:
                        isSelected
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.8),
                    size: isSelected ? 24 : 20,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    tab['shortTitle'],
                    style: TextStyle(
                      color:
                          isSelected
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.8),
                      fontSize: FontSizes.bodySmall,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildSearchResults() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search Results (${_searchResults.length})',
            style: TextStyle(
              fontSize: FontSizes.heading3,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final parameter = _searchResults[index];
                final tabTitle = _tabs[parameter['tab']]['title'];

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: _buildGlassmorphicContainer(
                    cacheKey:
                        'search_${parameter['title']}_${parameter['tab']}',
                    child: ListTile(
                      title: Text(
                        parameter['title'],
                        style: TextStyle(
                          fontSize: FontSizes.bodyLarge,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      subtitle: Text(
                        'Found in: $tabTitle',
                        style: TextStyle(
                          fontSize: FontSizes.bodyMedium,
                          color: Colors.white.withValues(alpha: 0.8),
                        ),
                      ),
                      trailing: Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white.withValues(alpha: 0.8),
                        size: 16,
                      ),
                      onTap: () => _navigateToParameter(parameter),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDiastolicGradingTable() {
    return _buildGlassmorphicContainer(
      cacheKey: 'diastolic_grading_table',
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Diastolic Function Grading',
              style: TextStyle(
                fontSize: FontSizes.heading3,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 120,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.15),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 120,
                        height: 56,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                          ),
                        ),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Parameter',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: FontSizes.bodyMedium,
                            ),
                          ),
                        ),
                      ),
                      _buildParameterCell('E/A Ratio'),
                      _buildParameterCell('DT (ms)'),
                      _buildParameterCell('IVRT (ms)'),
                      _buildParameterCell('S/D Ratio'),
                      _buildParameterCell('AR Duration (ms)'),
                      _buildParameterCell('AR Speed (cm/s)'),
                      _buildParameterCell('e\' Speed (cm/s)'),
                    ],
                  ),
                ),

                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      headingRowColor: WidgetStateProperty.all(
                        Colors.white.withValues(alpha: 0.1),
                      ),
                      dataRowColor: WidgetStateProperty.all(
                        Colors.white.withValues(alpha: 0.05),
                      ),
                      border: TableBorder.all(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                      headingRowHeight: 56,
                      dataRowMinHeight: 56,
                      dataRowMaxHeight: 56,
                      headingTextStyle: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: FontSizes.bodyMedium,
                      ),
                      dataTextStyle: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: FontSizes.bodySmall,
                      ),
                      columns: const [
                        DataColumn(label: Text('Normal Young')),
                        DataColumn(label: Text('Normal Adult')),
                        DataColumn(label: Text('Grade 1')),
                        DataColumn(label: Text('Grade 2')),
                        DataColumn(label: Text('Grade 3')),
                        DataColumn(label: Text('Grade 4')),
                      ],
                      rows: [
                        DataRow(
                          cells: [
                            DataCell(Text('1-2')),
                            DataCell(Text('1-2')),
                            DataCell(Text('<1')),
                            DataCell(Text('1-1.5')),
                            DataCell(Text('>1.5')),
                            DataCell(Text('1.5-2.0')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(Text('<240')),
                            DataCell(Text('150-240')),
                            DataCell(Text('≥240')),
                            DataCell(Text('150-200')),
                            DataCell(Text('<150')),
                            DataCell(Text('<150')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(Text('70-90')),
                            DataCell(Text('70-90')),
                            DataCell(Text('>90')),
                            DataCell(Text('<90')),
                            DataCell(Text('<70')),
                            DataCell(Text('<70')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(Text('<1')),
                            DataCell(Text('≥1')),
                            DataCell(Text('≥1')),
                            DataCell(Text('<1')),
                            DataCell(Text('<1')),
                            DataCell(Text('<1')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(Text('≥30')),
                            DataCell(Text('≤0')),
                            DataCell(Text('≤0 or ≥30')),
                            DataCell(Text('≥30')),
                            DataCell(Text('≥30')),
                            DataCell(Text('≥30')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(Text('<35')),
                            DataCell(Text('<35')),
                            DataCell(Text('<35')),
                            DataCell(Text('≥35')),
                            DataCell(Text('≥35')),
                            DataCell(Text('≥35')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(Text('>10')),
                            DataCell(Text('>8')),
                            DataCell(Text('<8')),
                            DataCell(Text('<8')),
                            DataCell(Text('<8')),
                            DataCell(Text('<8')),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Clinical Significance: Grade 1 = impaired relaxation, Grade 2 = pseudonormal (elevated filling pressures), Grade 3 = reversible restrictive filling, Grade 4 = irreversible restrictive filling (worst prognosis)',
              style: TextStyle(
                fontSize: FontSizes.bodySmall,
                color: Colors.white.withValues(alpha: 0.8),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParameterCell(String text) {
    return Container(
      width: 120,
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          text,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.9),
            fontSize: FontSizes.bodySmall,
          ),
        ),
      ),
    );
  }

  Widget _buildWallMotionCell(String text) {
    return Container(
      width: 120,
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          text,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.9),
            fontSize: FontSizes.bodySmall,
          ),
        ),
      ),
    );
  }

  Widget _buildRegionalWallMotionTable() {
    return _buildGlassmorphicContainer(
      cacheKey: 'regional_wall_motion_table',
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Regional Wall Motion Scoring',
              style: TextStyle(
                fontSize: FontSizes.heading3,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 120,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.15),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: 120,
                        height: 56,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 16,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.white.withValues(alpha: 0.3),
                            ),
                          ),
                        ),
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            'Motion',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: FontSizes.bodyMedium,
                            ),
                          ),
                        ),
                      ),
                      _buildWallMotionCell('Normal'),
                      _buildWallMotionCell('Hypokinesia'),
                      _buildWallMotionCell('Akinesia'),
                      _buildWallMotionCell('Dyskinesia'),
                      _buildWallMotionCell('Aneurysmal'),
                    ],
                  ),
                ),

                Expanded(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      headingRowColor: WidgetStateProperty.all(
                        Colors.white.withValues(alpha: 0.1),
                      ),
                      dataRowColor: WidgetStateProperty.all(
                        Colors.white.withValues(alpha: 0.05),
                      ),
                      border: TableBorder.all(
                        color: Colors.white.withValues(alpha: 0.3),
                      ),
                      headingRowHeight: 56,
                      dataRowMinHeight: 56,
                      dataRowMaxHeight: 56,
                      headingTextStyle: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: FontSizes.bodyMedium,
                      ),
                      dataTextStyle: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: FontSizes.bodySmall,
                      ),
                      columns: const [
                        DataColumn(label: Text('Definition')),
                        DataColumn(label: Text('Wall Thickening')),
                        DataColumn(label: Text('Wall Motion Score')),
                      ],
                      rows: [
                        DataRow(
                          cells: [
                            DataCell(
                              Text(
                                'Normal thickening (typically >30% thickening from end-diastole to end-systole)',
                              ),
                            ),
                            DataCell(Text('>30% thickening')),
                            DataCell(Text('1')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(
                              Text(
                                'Reduced thickening (typically 10–30% thickening from end-diastole to end-systole)',
                              ),
                            ),
                            DataCell(Text('10-30% thickening')),
                            DataCell(Text('2')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(
                              Text('Markedly reduced, or no thickening (<10%)'),
                            ),
                            DataCell(Text('<10% thickening')),
                            DataCell(Text('3')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(
                              Text(
                                'Paradoxical thinning and/or outward directed motion during systole',
                              ),
                            ),
                            DataCell(Text('Paradoxical thinning')),
                            DataCell(Text('4')),
                          ],
                        ),
                        DataRow(
                          cells: [
                            DataCell(
                              Text('Wall deformation with outward bulging'),
                            ),
                            DataCell(Text('Diastolic bulging')),
                            DataCell(Text('5')),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Clinical Significance: Wall Motion Score Index (WMSI) = Sum of scores ÷ Number of segments. Normal WMSI = 1.0. WMSI >1.7 indicates significant LV dysfunction. Used for detecting coronary artery disease, assessing myocardial viability, and risk stratification.',
              style: TextStyle(
                fontSize: FontSizes.bodySmall,
                color: Colors.white.withValues(alpha: 0.8),
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              '17-Segment Model: Basal (1-6), Mid (7-12), Apical (13-16), Apex (17). Each segment scored 1-5. Recommended by American Society of Echocardiography.',
              style: TextStyle(
                fontSize: FontSizes.bodySmall,
                color: Colors.white.withValues(alpha: 0.8),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return _buildLeftVentricleContent();
      case 1:
        return _buildLeftAtriumContent();
      case 2:
        return _buildRightVentricleContent();
      case 3:
        return _buildRightAtriumContent();
      case 4:
        return _buildValvesContent();
      case 5:
        return _buildAortaContent();
      default:
        return _buildLeftVentricleContent();
    }
  }

  Widget _buildLeftVentricleContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernSectionHeader(
            'Left Ventricular Dimensions',
            FontAwesomeIcons.ruler,
            [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
            'dimensions',
          ),
          _buildCollapsibleSection(
            'dimensions',
            () => [
              _buildModernParameterCard(
                'LV Internal Diameter at End-Diastole (LVIDd)',
                'Women: 3.9-5.3 cm • Men: 4.2-5.9 cm (ASE 2015)',
                'BSA indexed: Women 2.4-3.2 cm/m² • Men 2.2-3.1 cm/m²',
                normalRange: [3.9, 5.9],
                currentValue: 4.5,
                unit: 'cm',
                abnormalRanges: {
                  'mildly_abnormal_women': [5.4, 5.7],
                  'moderately_abnormal_women': [5.8, 6.1],
                  'severely_abnormal_women': [6.2, 8.0],
                  'mildly_abnormal_men': [6.0, 6.3],
                  'moderately_abnormal_men': [6.4, 6.8],
                  'severely_abnormal_men': [6.9, 8.0],
                },
                clinicalImportance:
                    'LVIDd reflects LV preload and chamber size per ASE 2015 guidelines. Increased values suggest volume overload, dilated cardiomyopathy, or valvular regurgitation. Critical for calculating ejection fraction and stroke volume.',
              ),
              _buildModernParameterCard(
                'LV Internal Diameter at End-Systole (LVIDs)',
                'Women: 2.2-4.1 cm • Men: 2.5-4.5 cm',
                'BSA indexed: Women 1.4-2.6 cm/m² • Men 1.4-2.8 cm/m²',
                normalRange: [2.2, 4.5],
                currentValue: 3.1,
                unit: 'cm',
                abnormalRanges: {
                  'mild_female': [4.2, 4.4],
                  'moderate_female': [4.5, 4.7],
                  'severe_female': [4.8, 8.0],
                  'mild_male': [4.6, 4.9],
                  'moderate_male': [5.0, 5.3],
                  'severe_male': [5.4, 8.0],
                },
                clinicalImportance:
                    'LVIDs reflects LV contractility and afterload. Increased values indicate poor systolic function, heart failure, or increased afterload. Essential for calculating fractional shortening and ejection fraction. Strong predictor of cardiovascular outcomes.',
              ),
              _buildModernParameterCard(
                'Interventricular Septal Thickness (IVSd)',
                'Women: 0.6-0.9 cm • Men: 0.6-1.0 cm',
                'Measured at end-diastole in parasternal long axis view',
                normalRange: [0.6, 1.0],
                currentValue: 0.8,
                unit: 'cm',
                abnormalRanges: {
                  'mild_female': [1.0, 1.2],
                  'moderate_female': [1.3, 1.5],
                  'severe_female': [1.6, 3.0],
                  'mild_male': [1.1, 1.3],
                  'moderate_male': [1.4, 1.6],
                  'severe_male': [1.7, 3.0],
                },
                clinicalImportance:
                    'Septal thickness indicates LV hypertrophy, often due to hypertension, aortic stenosis, or hypertrophic cardiomyopathy. Asymmetric septal hypertrophy (ASH) suggests HOCM. Thin septum may indicate dilated cardiomyopathy or normal variant.',
              ),
              _buildModernParameterCard(
                'Posterior Wall Thickness (PWd)',
                'Women: 0.6-0.9 cm • Men: 0.6-1.0 cm',
                'Measured at end-diastole in parasternal long axis view',
                normalRange: [0.6, 1.0],
                currentValue: 0.8,
                unit: 'cm',
                abnormalRanges: {
                  'mild_female': [1.0, 1.2],
                  'moderate_female': [1.3, 1.5],
                  'severe_female': [1.6, 3.0],
                  'mild_male': [1.1, 1.3],
                  'moderate_male': [1.4, 1.6],
                  'severe_male': [1.7, 3.0],
                },
                clinicalImportance:
                    'Posterior wall thickness helps assess LV hypertrophy pattern. Concentric hypertrophy (increased septal and posterior wall thickness) suggests pressure overload. Used to calculate relative wall thickness and LV mass.',
              ),
              _buildModernParameterCard(
                'LV Diastolic Diameter/Height',
                'Women: 2.5-3.2 cm/m • Men: 2.4-3.3 cm/m',
                'Height-indexed diameter, alternative to BSA indexing',
                normalRange: [2.4, 3.3],
                currentValue: 2.8,
                unit: 'cm/m',
                abnormalRanges: {
                  'mild_female': [3.3, 3.5],
                  'moderate_female': [3.6, 3.8],
                  'severe_female': [3.9, 5.0],
                  'mild_male': [3.4, 3.6],
                  'moderate_male': [3.7, 3.9],
                  'severe_male': [4.0, 5.0],
                },
                clinicalImportance:
                    'Height-indexed LV diameter provides alternative assessment independent of body weight. Useful in obese patients where BSA indexing may underestimate chamber enlargement. Better correlation with outcomes in some populations.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Left Ventricular Volumes',
            FontAwesomeIcons.cube,
            [Color(0xFF9B59B6), Color(0xFFBB7FD1)],
            'volumes',
          ),
          _buildCollapsibleSection(
            'volumes',
            () => [
              _buildModernParameterCard(
                'LV End-Diastolic Volume (LVEDV)',
                'Women: 56-104 mL • Men: 67-155 mL',
                'BSA indexed: Women 35-75 mL/m² • Men 34-74 mL/m²',
                normalRange: [56, 155],
                currentValue: 85,
                unit: 'mL',
                abnormalRanges: {
                  'mild_female': [105, 117],
                  'moderate_female': [118, 130],
                  'severe_female': [131, 400],
                  'mild_male': [156, 178],
                  'moderate_male': [179, 201],
                  'severe_male': [202, 400],
                },
                clinicalImportance:
                    'LVEDV represents LV preload and filling capacity. Increased values indicate volume overload, valvular regurgitation, or dilated cardiomyopathy. Essential for calculating ejection fraction and stroke volume. Strong predictor of heart failure and mortality.',
              ),
              _buildModernParameterCard(
                'LV End-Systolic Volume (LVESV)',
                'Women: 19-49 mL • Men: 22-58 mL',
                'BSA indexed: Women 12-30 mL/m² • Men 11-31 mL/m²',
                normalRange: [19, 58],
                currentValue: 35,
                unit: 'mL',
                abnormalRanges: {
                  'mild_female': [50, 59],
                  'moderate_female': [60, 69],
                  'severe_female': [70, 300],
                  'mild_male': [59, 70],
                  'moderate_male': [71, 82],
                  'severe_male': [83, 300],
                },
                clinicalImportance:
                    'LVESV reflects LV contractility and systolic function. Increased values indicate impaired systolic function, heart failure, or increased afterload. More sensitive than EF for detecting early systolic dysfunction. Critical for risk stratification.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Left Ventricular Function',
            FontAwesomeIcons.chartLine,
            [Color(0xFF4ECDC4), Color(0xFF6EE7E0)],
            'function',
          ),
          _buildCollapsibleSection(
            'function',
            () => [
              _buildModernParameterCard(
                'Ejection Fraction (EF)',
                'Women: 54-74% • Men: 52-72%',
                'Most important parameter for systolic function assessment',
                normalRange: [52, 74],
                currentValue: 65,
                unit: '%',
                abnormalRanges: {
                  'mild_female': [50, 53],
                  'moderate_female': [30, 49],
                  'severe_female': [0, 29],
                  'mild_male': [45, 51],
                  'moderate_male': [30, 44],
                  'severe_male': [0, 29],
                },
                clinicalImportance:
                    'EF is the gold standard for assessing LV systolic function. Reduced EF indicates heart failure with reduced ejection fraction (HFrEF). Critical for prognosis, treatment decisions, and ICD/CRT candidacy. Values <35% indicate severe dysfunction requiring aggressive therapy.',
              ),
              _buildModernParameterCard(
                'Fractional Shortening (FS)',
                'Women: 27-45% • Men: 25-43% • Normal: >25%',
                'M-mode derived parameter, less accurate than EF',
                normalRange: [25, 45],
                currentValue: 35,
                unit: '%',
                abnormalRanges: {
                  'mild_female': [22, 26],
                  'moderate_female': [17, 21],
                  'severe_female': [0, 16],
                  'mild_male': [20, 24],
                  'moderate_male': [15, 19],
                  'severe_male': [0, 14],
                },
                clinicalImportance:
                    'FS reflects radial systolic function. Less reliable than EF but useful when 2D images are suboptimal. Decreased FS indicates systolic dysfunction. May be preserved in circumferential dysfunction when radial function is maintained.',
              ),
              _buildModernParameterCard(
                'Midwall Fractional Shortening',
                'Women: 15-23% • Men: 14-22%',
                'More sensitive than endocardial FS for detecting early dysfunction',
                normalRange: [14, 23],
                currentValue: 18,
                unit: '%',
                abnormalRanges: {
                  'mild_female': [12, 14],
                  'moderate_female': [9, 11],
                  'severe_female': [0, 8],
                  'mild_male': [11, 13],
                  'moderate_male': [8, 10],
                  'severe_male': [0, 7],
                },
                clinicalImportance:
                    'Midwall FS is more sensitive than endocardial FS for detecting early systolic dysfunction, especially in concentric LVH. Accounts for wall thickness effects on fiber shortening. Better predictor of outcomes than endocardial FS in hypertensive patients.',
              ),
              _buildModernParameterCard(
                'Ejection Fraction (Biplane)',
                'Women: 54-74% • Men: 52-72%',
                'Biplane method using Simpson\'s rule, more accurate than single plane',
                normalRange: [52, 74],
                currentValue: 63,
                unit: '%',
                abnormalRanges: {
                  'mild_female': [50, 53],
                  'moderate_female': [30, 49],
                  'severe_female': [0, 29],
                  'mild_male': [45, 51],
                  'moderate_male': [30, 44],
                  'severe_male': [0, 29],
                },
                clinicalImportance:
                    'Biplane EF using Simpson\'s rule is more accurate than single-plane measurements. Recommended method for EF calculation when adequate image quality is available. More precise assessment of LV systolic function.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Left Ventricular Mass',
            FontAwesomeIcons.weightScale,
            [Color(0xFFFF9500), Color(0xFFFFB347)],
            'mass',
          ),
          _buildCollapsibleSection(
            'mass',
            () => [
              _buildModernParameterCard(
                'LV Mass (Linear Method)',
                'Women: 67-162 g • Men: 88-224 g',
                'Linear method using ASE formula: 0.8 × {1.04[(LVIDd + PWTd + SWTd)³ - LVIDd³]} + 0.6',
                normalRange: [67, 224],
                currentValue: 125,
                unit: 'g',
                abnormalRanges: {
                  'mild_female': [163, 186],
                  'moderate_female': [187, 210],
                  'severe_female': [211, 400],
                  'mild_male': [225, 258],
                  'moderate_male': [259, 292],
                  'severe_male': [293, 500],
                },
                clinicalImportance:
                    'LV mass reflects myocardial hypertrophy response to pressure/volume overload. Increased mass indicates LVH due to hypertension, aortic stenosis, or cardiomyopathy. Strong predictor of cardiovascular events and mortality. Used to classify LVH patterns.',
              ),
              _buildModernParameterCard(
                'LV Mass (2D Method)',
                'Women: 66-150 g • Men: 96-200 g',
                '2D method using area-length formula, more accurate than linear method',
                normalRange: [66, 200],
                currentValue: 120,
                unit: 'g',
                abnormalRanges: {
                  'mild_female': [151, 171],
                  'moderate_female': [172, 192],
                  'severe_female': [193, 400],
                  'mild_male': [201, 227],
                  'moderate_male': [228, 254],
                  'severe_male': [255, 500],
                },
                clinicalImportance:
                    'More accurate than linear method for calculating LV mass. Essential for diagnosing LVH and monitoring regression with treatment. Helps distinguish between concentric and eccentric hypertrophy patterns. Critical for risk stratification.',
              ),
              _buildModernParameterCard(
                'Relative Wall Thickness (RWT)',
                'Women: 0.22-0.42 • Men: 0.24-0.42',
                'RWT = 2 × posterior wall thickness / LVIDd',
                normalRange: [0.22, 0.42],
                currentValue: 0.32,
                unit: '',
                abnormalRanges: {
                  'mild_female': [0.43, 0.47],
                  'moderate_female': [0.48, 0.52],
                  'severe_female': [0.53, 1.0],
                  'mild_male': [0.43, 0.47],
                  'moderate_male': [0.48, 0.52],
                  'severe_male': [0.53, 1.0],
                },
                clinicalImportance:
                    'RWT distinguishes LVH patterns: Normal geometry (normal mass, normal RWT), concentric remodeling (normal mass, increased RWT), eccentric LVH (increased mass, normal RWT), concentric LVH (increased mass, increased RWT). Guides treatment strategies.',
              ),
              _buildModernParameterCard(
                'LV Mass/Height',
                'Women: 41-99 g/m • Men: 52-126 g/m',
                'Height-indexed LV mass provides alternative assessment independent of body weight',
                normalRange: [41, 126],
                currentValue: 75,
                unit: 'g/m',
                abnormalRanges: {
                  'mild_female': [100, 115],
                  'moderate_female': [116, 128],
                  'severe_female': [129, 200],
                  'mild_male': [127, 144],
                  'moderate_male': [145, 162],
                  'severe_male': [163, 250],
                },
                clinicalImportance:
                    'Height-indexed LV mass provides alternative assessment independent of body weight. Useful in obese patients where BSA indexing may underestimate LVH. Better correlation with cardiovascular outcomes in some populations.',
              ),
              _buildModernParameterCard(
                'LV Mass/Height^2.7',
                'Women: 18-44 g/m^2.7 • Men: 20-48 g/m^2.7',
                'Allometric height scaling (height^2.7) provides the most accurate indexing method',
                normalRange: [18, 48],
                currentValue: 32,
                unit: 'g/m^2.7',
                abnormalRanges: {
                  'mild_female': [45, 51],
                  'moderate_female': [52, 58],
                  'severe_female': [59, 80],
                  'mild_male': [49, 55],
                  'moderate_male': [56, 63],
                  'severe_male': [64, 90],
                },
                clinicalImportance:
                    'Allometric height scaling (height^2.7) provides the most accurate indexing method for LV mass. Eliminates height-related bias better than linear or BSA indexing. Recommended for research and when precise LVH assessment is critical.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Diastolic Function',
            FontAwesomeIcons.waveSquare,
            [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
            'diastolic',
          ),
          _buildCollapsibleSection(
            'diastolic',
            () => [
              _buildDiastolicGradingTable(),
              const SizedBox(height: 16),
              _buildModernParameterCard(
                'Mitral Inflow E/A Ratio',
                'Young adults (20-40y): 1.3-2.5 • Middle age (40-60y): 0.8-1.5 • Elderly (>60y): 0.6-1.0',
                'Age-dependent parameter, decreases with normal aging',
                normalRange: [0.6, 2.5],
                currentValue: 1.4,
                unit: '',
                abnormalRanges: {
                  'mild_young': [0.8, 1.2],
                  'moderate_young': [2.6, 3.0],
                  'severe_young': [0.0, 0.7],
                  'mild_elderly': [0.5, 0.59],
                  'moderate_elderly': [1.1, 1.5],
                  'severe_elderly': [0.0, 0.49],
                },
                clinicalImportance:
                    'E/A ratio reflects LV filling pattern. Grade 1 diastolic dysfunction shows impaired relaxation (E/A <1). Pseudonormal pattern (Grade 2) may appear normal but represents elevated filling pressures. Restrictive pattern (Grade 3) indicates severe dysfunction.',
              ),
              _buildModernParameterCard(
                'Deceleration Time (DT)',
                'Young adults (20-40y): 150-200 ms • Middle age (40-60y): 160-240 ms • Elderly (>60y): 200-280 ms',
                'Time for E-wave velocity to decrease to baseline',
                normalRange: [150, 280],
                currentValue: 195,
                unit: 'ms',
                abnormalRanges: {
                  'mild_young': [201, 240],
                  'moderate_young': [120, 149],
                  'severe_young': [0, 119],
                  'mild_elderly': [281, 320],
                  'moderate_elderly': [140, 199],
                  'severe_elderly': [0, 139],
                },
                clinicalImportance:
                    'DT reflects LV compliance and filling pressures. Prolonged DT indicates impaired relaxation (Grade 1). Shortened DT suggests elevated filling pressures and restrictive physiology. Combined with E/A ratio helps grade diastolic dysfunction.',
              ),
              _buildModernParameterCard(
                'Tissue Doppler e\' velocity',
                'Septal: >7 cm/s • Lateral: >10 cm/s • Average: >8 cm/s (ASE 2015)',
                'Direct measure of myocardial relaxation, preload independent',
                normalRange: [7, 15],
                currentValue: 10,
                unit: 'cm/s',
                abnormalRanges: {
                  'abnormal_septal': [0, 6.9],
                  'abnormal_lateral': [0, 9.9],
                },
                clinicalImportance:
                    'e\' velocity is the most reliable parameter for assessing diastolic function per ASE 2015 guidelines. Reduced e\' indicates impaired myocardial relaxation. Essential for calculating E/e\' ratio.',
              ),
              _buildModernParameterCard(
                'E/e\' Ratio',
                'Normal: <8 • Intermediate: 8-15 • Elevated: >15 (ASE 2015)',
                'Best predictor of LV filling pressures • Use average of septal and lateral e\'',
                normalRange: [4, 8],
                currentValue: 6.5,
                unit: '',
                abnormalRanges: {
                  'intermediate': [8, 15],
                  'elevated': [16, 35],
                },
                clinicalImportance:
                    'E/e\' ratio is the best non-invasive predictor of LV filling pressures per ASE 2015 guidelines. Values >15 indicate elevated LVEDP. Intermediate values (8-15) require additional parameters.',
              ),
              _buildModernParameterCard(
                'Isovolumic Relaxation Time (IVRT)',
                'Young adults: 70-90 ms • Middle age: 80-100 ms • Elderly: 90-110 ms',
                'Time from aortic valve closure to mitral valve opening',
                normalRange: [70, 110],
                currentValue: 85,
                unit: 'ms',
                abnormalRanges: {
                  'mild_young': [91, 120],
                  'moderate_young': [50, 69],
                  'severe_young': [0, 49],
                  'mild_elderly': [111, 140],
                  'moderate_elderly': [60, 89],
                  'severe_elderly': [0, 59],
                },
                clinicalImportance:
                    'IVRT reflects LV relaxation rate. Prolonged IVRT indicates impaired relaxation (Grade 1 dysfunction). Shortened IVRT suggests elevated filling pressures and restrictive physiology. Age-dependent parameter.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Advanced Systolic Function',
            FontAwesomeIcons.chartLine,
            [Color(0xFF9B59B6), Color(0xFFBB7FD1)],
            'advancedSystolic',
          ),
          _buildCollapsibleSection(
            'advancedSystolic',
            () => [
              _buildModernParameterCard(
                'Global Longitudinal Strain (GLS)',
                'Normal: <-18% • Abnormal: >-16%',
                'Most sensitive parameter for detecting early systolic dysfunction (ASE 2015)',
                normalRange: [18, 24],
                currentValue: 20,
                unit: '%',
                abnormalRanges: {
                  'abnormal': [0, 16],
                },
                clinicalImportance:
                    'GLS is the most sensitive parameter for detecting subclinical LV dysfunction per ASE 2015 guidelines. Values worse than -16% indicate significant dysfunction. Critical for early detection of cardiotoxicity, ischemia, and cardiomyopathy.',
              ),
              _buildModernParameterCard(
                'S\' (S prime) Tissue Velocity - Septal',
                'Normal: >8 cm/s • Abnormal: <8 cm/s',
                'Direct measure of longitudinal systolic function - septal annulus (ASE 2015)',
                normalRange: [8, 15],
                currentValue: 10,
                unit: 'cm/s',
                abnormalRanges: {
                  'abnormal': [0, 7.9],
                },
                clinicalImportance:
                    'Septal S\' velocity reflects longitudinal systolic function per ASE 2015 guidelines. More sensitive than EF for detecting early systolic dysfunction.',
              ),
              _buildModernParameterCard(
                'S\' (S prime) Tissue Velocity - Lateral',
                'Normal: >10 cm/s • Abnormal: <10 cm/s',
                'Direct measure of longitudinal systolic function - lateral annulus (ASE 2015)',
                normalRange: [10, 18],
                currentValue: 12,
                unit: 'cm/s',
                abnormalRanges: {
                  'abnormal': [0, 9.9],
                },
                clinicalImportance:
                    'Lateral S\' velocity typically higher than septal per ASE 2015 guidelines. Combined with septal S\' provides comprehensive assessment of longitudinal function.',
              ),
              _buildModernParameterCard(
                'MAPSE (Mitral Annular Plane Systolic Excursion)',
                'Normal: >11 mm • Abnormal: <11 mm',
                'Simple, reproducible measure of longitudinal LV function (ASE 2015)',
                normalRange: [11, 20],
                currentValue: 15,
                unit: 'mm',
                abnormalRanges: {
                  'abnormal': [0, 10.9],
                },
                clinicalImportance:
                    'MAPSE reflects longitudinal LV systolic function per ASE 2015 guidelines. Easy to measure with high reproducibility. Particularly useful in patients with poor acoustic windows.',
              ),
              _buildModernParameterCard(
                'dP/dt (Rate of Pressure Rise)',
                'Normal: >1000 mmHg/s • Abnormal: <1000 mmHg/s',
                'Measured from mitral regurgitation jet • Load-independent contractility (ASE 2015)',
                normalRange: [1000, 2500],
                currentValue: 1200,
                unit: 'mmHg/s',
                abnormalRanges: {
                  'abnormal': [0, 999],
                },
                clinicalImportance:
                    'dP/dt reflects LV contractility independent of preload and afterload per ASE 2015 guidelines. Requires adequate mitral regurgitation for measurement.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Advanced Strain Parameters',
            FontAwesomeIcons.waveSquare,
            [Color(0xFF4ECDC4), Color(0xFF6EE7E0)],
            'strain',
          ),
          _buildCollapsibleSection(
            'strain',
            () => [
              _buildModernParameterCard(
                'Global Circumferential Strain (GCS)',
                'Normal: >-20% • Borderline: -15% to -20% • Abnormal: >-15%',
                'Reflects circumferential myocardial deformation (ASE 2015)',
                normalRange: [20, 30],
                currentValue: 22,
                unit: '%',
                abnormalRanges: {
                  'borderline': [15, 19],
                  'abnormal': [0, 14],
                },
                clinicalImportance:
                    'GCS reflects circumferential myocardial function per ASE 2015 guidelines. Less sensitive than GLS but provides complementary information. Reduced GCS may indicate circumferential fiber dysfunction.',
              ),
              _buildModernParameterCard(
                'Global Radial Strain (GRS)',
                'Normal: >35% • Borderline: 25-35% • Abnormal: <25%',
                'Reflects radial myocardial thickening during systole (ASE 2015)',
                normalRange: [35, 60],
                currentValue: 45,
                unit: '%',
                abnormalRanges: {
                  'borderline': [25, 34],
                  'abnormal': [0, 24],
                },
                clinicalImportance:
                    'GRS reflects radial myocardial function per ASE 2015 guidelines. Most variable strain parameter with highest measurement variability. Less reliable than longitudinal strain for clinical decisions.',
              ),
              _buildModernParameterCard(
                'LV Torsion/Twist',
                'Normal: 1-3°/cm • Borderline: 0.5-1°/cm • Abnormal: <0.5°/cm',
                'Apical rotation relative to base (ASE 2015)',
                normalRange: [1, 3],
                currentValue: 2.1,
                unit: '°/cm',
                abnormalRanges: {
                  'borderline': [0.5, 0.9],
                  'abnormal': [0.0, 0.4],
                },
                clinicalImportance:
                    'LV twist reflects the wringing motion of the heart during systole per ASE 2015 guidelines. Essential for efficient ejection and diastolic suction. Reduced twist indicates advanced cardiomyopathy.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Myocardial Performance',
            FontAwesomeIcons.heartCircleBolt,
            [Color(0xFFFF9500), Color(0xFFFFB347)],
            'performance',
          ),
          _buildCollapsibleSection(
            'performance',
            () => [
              _buildModernParameterCard(
                'MPI (Myocardial Performance Index)',
                'Normal: <0.4 • Borderline: 0.4-0.5 • Abnormal: >0.5',
                'Also known as Tei index, combines systolic and diastolic function (ASE 2015)',
                normalRange: [0.2, 0.4],
                currentValue: 0.3,
                unit: '',
                abnormalRanges: {
                  'borderline': [0.4, 0.5],
                  'abnormal': [0.5, 1.5],
                },
                clinicalImportance:
                    'MPI (Tei index) is a combined measure of systolic and diastolic function per ASE 2015 guidelines. Elevated MPI indicates global LV dysfunction. Independent predictor of cardiovascular outcomes.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Wall Motion Assessment',
            FontAwesomeIcons.heartPulse,
            [Color(0xFF4ECDC4), Color(0xFF6EE7E0)],
            'wallMotion',
          ),
          _buildCollapsibleSection(
            'wallMotion',
            () => [_buildRegionalWallMotionTable()],
          ),
        ],
      ),
    );
  }

  Widget _buildLeftAtriumContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernSectionHeader(
            'Left Atrial Dimensions',
            FontAwesomeIcons.ruler,
            [Color(0xFFFF9FF3), Color(0xFFFFB3F7)],
            'laDimensions',
          ),
          _buildCollapsibleSection(
            'laDimensions',
            () => [
              _buildModernParameterCard(
                'LA Diameter (Anteroposterior)',
                'Women: 2.7-3.8 cm • Men: 3.0-4.0 cm (ASE 2015)',
                'BSA indexed: Women 1.5-2.3 cm/m² • Men 1.5-2.3 cm/m²',
                normalRange: [2.7, 4.0],
                currentValue: 3.2,
                unit: 'cm',
                abnormalRanges: {
                  'mildly_abnormal_women': [3.9, 4.6],
                  'moderately_abnormal_women': [4.7, 5.2],
                  'severely_abnormal_women': [5.3, 7.0],
                  'mildly_abnormal_men': [4.1, 4.6],
                  'moderately_abnormal_men': [4.7, 5.2],
                  'severely_abnormal_men': [5.3, 7.0],
                },
                clinicalImportance:
                    'LA diameter reflects chronic LA pressure elevation per ASE 2015 guidelines. Enlarged LA indicates increased risk of atrial fibrillation, stroke, and heart failure.',
              ),
              _buildModernParameterCard(
                'LA Volume (Biplane Method)',
                'Normal: 16-34 mL/m² • Abnormal: >34 mL/m² (ASE 2015)',
                'BSA indexed volume using biplane area-length method',
                normalRange: [16, 34],
                currentValue: 24,
                unit: 'mL/m²',
                abnormalRanges: {
                  'mildly_abnormal': [35, 41],
                  'moderately_abnormal': [42, 48],
                  'severely_abnormal': [49, 80],
                },
                clinicalImportance:
                    'LA volume indexed to BSA is the most accurate measure of LA size per ASE 2015 guidelines. Values >34 mL/m² indicate LA enlargement. Strong predictor of cardiovascular events.',
              ),
              _buildModernParameterCard(
                'LA Volume Index (LAVI)',
                'Normal: <34 mL/m² • Abnormal: >34 mL/m² (ASE 2015)',
                'Primary parameter for LA size assessment',
                normalRange: [16, 34],
                currentValue: 28,
                unit: 'mL/m²',
                abnormalRanges: {
                  'abnormal': [35, 80],
                },
                clinicalImportance:
                    'LAVI is the primary parameter for LA size assessment per ASE 2015 guidelines. More accurate than diameter measurements. Critical for risk stratification in heart failure and atrial fibrillation.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Left Atrial Function (ASE 2015)',
            FontAwesomeIcons.heartCircleCheck,
            [Color(0xFF9B59B6), Color(0xFFBB7FD1)],
            'laFunction',
          ),
          _buildCollapsibleSection(
            'laFunction',
            () => [
              _buildModernParameterCard(
                'LA Emptying Fraction (Total)',
                'Normal: >50% • Abnormal: <50% (ASE 2015)',
                'Calculated as (LAVmax - LAVmin) / LAVmax × 100',
                normalRange: [50, 70],
                currentValue: 58,
                unit: '%',
                abnormalRanges: {
                  'abnormal': [0, 49],
                },
                clinicalImportance:
                    'LA emptying fraction reflects overall LA function per ASE 2015 guidelines. Reduced values indicate LA dysfunction and increased risk of atrial fibrillation.',
              ),
              _buildModernParameterCard(
                'Pulmonary Vein Flow (S/D Ratio)',
                'Normal: 0.8-1.2 • Abnormal: <0.8 (ASE 2015)',
                'S wave = systolic flow, D wave = diastolic flow',
                normalRange: [0.8, 1.2],
                currentValue: 1.0,
                unit: '',
                abnormalRanges: {
                  'abnormal': [0.0, 0.79],
                },
                clinicalImportance:
                    'PV S/D ratio reflects LA pressure and compliance per ASE 2015 guidelines. Reduced ratio indicates elevated LA pressure. Useful for grading diastolic dysfunction.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Pulmonary Vein Assessment',
            FontAwesomeIcons.lungs,
            [Color(0xFF3498DB), Color(0xFF5DADE2)],
            'laPulmonaryVeins',
          ),
          _buildCollapsibleSection(
            'laPulmonaryVeins',
            () => [
              _buildModernParameterCard(
                'Right Upper Pulmonary Vein (RUPV) Diameter',
                'Normal: 10-15 mm (ASE 2015)',
                'Measured at vein ostium 1-2 cm from LA junction',
                normalRange: [10, 15],
                currentValue: 12,
                unit: 'mm',
                abnormalRanges: {
                  'mild': [16, 20],
                  'moderate': [21, 25],
                  'severe': [26, 35],
                },
                clinicalImportance:
                    'RUPV diameter per ASE 2015 guidelines. Enlarged PV indicates chronic LA pressure elevation and increased risk of atrial fibrillation. Critical for pre-ablation assessment.',
              ),
              _buildModernParameterCard(
                'Right Lower Pulmonary Vein (RLPV) Diameter',
                'Normal: 10-15 mm (ASE 2015)',
                'Measured at vein ostium 1-2 cm from LA junction',
                normalRange: [10, 15],
                currentValue: 12,
                unit: 'mm',
                abnormalRanges: {
                  'mild': [16, 20],
                  'moderate': [21, 25],
                  'severe': [26, 35],
                },
                clinicalImportance:
                    'RLPV diameter per ASE 2015 guidelines. Enlarged PV indicates chronic LA pressure elevation and increased risk of atrial fibrillation. Critical for pre-ablation assessment.',
              ),
              _buildModernParameterCard(
                'Left Upper Pulmonary Vein (LUPV) Diameter',
                'Normal: 10-15 mm (ASE 2015)',
                'Measured at vein ostium 1-2 cm from LA junction',
                normalRange: [10, 15],
                currentValue: 12,
                unit: 'mm',
                abnormalRanges: {
                  'mild': [16, 20],
                  'moderate': [21, 25],
                  'severe': [26, 35],
                },
                clinicalImportance:
                    'LUPV diameter per ASE 2015 guidelines. Enlarged PV indicates chronic LA pressure elevation and increased risk of atrial fibrillation. Critical for pre-ablation assessment.',
              ),
              _buildModernParameterCard(
                'Left Lower Pulmonary Vein (LLPV) Diameter',
                'Normal: 12-16 mm • Mild: 16-20 mm',
                'Measured at vein ostium in TEE or 3D echo',
                normalRange: [12, 16],
                currentValue: 14,
                unit: 'mm',
                abnormalRanges: {
                  'mild': [16, 20],
                  'moderate': [20, 25],
                  'severe': [25, 35],
                },
                clinicalImportance:
                    'LLPV diameter completes individual PV assessment. Important for detecting anatomical variants and planning ablation strategy.',
              ),
              _buildModernParameterCard(
                'PV Flow Reversal Duration',
                'Normal: <35 ms • Abnormal: >35 ms',
                'Duration of atrial reversal wave in pulmonary veins',
                normalRange: [15, 35],
                currentValue: 25,
                unit: 'ms',
                abnormalRanges: {
                  'mild': [35, 50],
                  'moderate': [50, 70],
                  'severe': [70, 120],
                },
                clinicalImportance:
                    'PV flow reversal duration reflects LA pressure and compliance. Prolonged reversal indicates elevated LVEDP and diastolic dysfunction. Useful for grading diastolic dysfunction severity.',
              ),
              _buildModernParameterCard(
                'PV Systolic Fraction',
                'Normal: >55% • Borderline: 45-55%',
                'Percentage of total PV flow occurring during systole',
                normalRange: [55, 75],
                currentValue: 65,
                unit: '%',
                abnormalRanges: {
                  'mild': [45, 54],
                  'moderate': [35, 44],
                  'severe': [20, 34],
                },
                clinicalImportance:
                    'PV systolic fraction reflects LA compliance and filling pressures. Reduced systolic fraction indicates elevated LA pressure and impaired LA compliance. Important for diastolic function assessment.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Advanced LA Parameters',
            FontAwesomeIcons.chartLine,
            [Color(0xFFFF9500), Color(0xFFFFB347)],
            'laAdvanced',
          ),
          _buildCollapsibleSection(
            'laAdvanced',
            () => [
              _buildModernParameterCard(
                'LA Reservoir Strain',
                'Normal: >35% • Borderline: 25-35%',
                'Peak positive strain during ventricular systole',
                normalRange: [35, 50],
                currentValue: 42,
                unit: '%',
                abnormalRanges: {
                  'mild': [25, 34],
                  'moderate': [15, 24],
                  'severe': [0, 14],
                },
                clinicalImportance:
                    'LA reservoir strain reflects LA compliance and filling capacity. Reduced values indicate LA fibrosis and dysfunction. Early marker of LA remodeling before size changes. Strong predictor of atrial fibrillation and cardiovascular events.',
              ),
              _buildModernParameterCard(
                'LA Conduit Strain',
                'Normal: >15% • Borderline: 10-15%',
                'Strain during early ventricular diastole',
                normalRange: [15, 25],
                currentValue: 18,
                unit: '%',
                abnormalRanges: {
                  'mild': [10, 14],
                  'moderate': [5, 9],
                  'severe': [0, 4],
                },
                clinicalImportance:
                    'LA conduit strain reflects passive LA emptying and LV relaxation. Reduced values indicate impaired LV relaxation and elevated filling pressures. Correlates with diastolic dysfunction severity.',
              ),
              _buildModernParameterCard(
                'LA Contractile Strain',
                'Normal: >15% • Borderline: 10-15%',
                'Strain during late ventricular diastole (atrial kick)',
                normalRange: [15, 25],
                currentValue: 18,
                unit: '%',
                abnormalRanges: {
                  'mild': [10, 14],
                  'moderate': [5, 9],
                  'severe': [0, 4],
                },
                clinicalImportance:
                    'LA contractile strain reflects intrinsic LA contractile function. Reduced values indicate LA myopathy and dysfunction. Lost in atrial fibrillation. Important for maintaining cardiac output in patients with diastolic dysfunction.',
              ),
              _buildModernParameterCard(
                'LA Stiffness Index',
                'Normal: <0.4 • Borderline: 0.4-0.6',
                'Calculated as E/e\' / LA reservoir strain',
                normalRange: [0.1, 0.4],
                currentValue: 0.25,
                unit: '',
                abnormalRanges: {
                  'mild': [0.4, 0.6],
                  'moderate': [0.6, 0.8],
                  'severe': [0.8, 2.0],
                },
                clinicalImportance:
                    'LA stiffness index combines filling pressures and LA function. Elevated values indicate increased LA stiffness and poor compliance. Strong predictor of cardiovascular outcomes and atrial fibrillation recurrence after ablation.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'LA Strain Rate Parameters',
            FontAwesomeIcons.gaugeHigh,
            [Color(0xFFE67E22), Color(0xFFF39C12)],
            'laStrainRate',
          ),
          _buildCollapsibleSection(
            'laStrainRate',
            () => [
              _buildModernParameterCard(
                'Peak LA Reservoir Strain Rate (SRs)',
                'Normal: >1.5 s⁻¹ • Borderline: 1.0-1.5 s⁻¹',
                'Peak positive strain rate during ventricular systole',
                normalRange: [1.5, 3.0],
                currentValue: 2.2,
                unit: 's⁻¹',
                abnormalRanges: {
                  'mild': [1.0, 1.4],
                  'moderate': [0.7, 0.9],
                  'severe': [0.3, 0.6],
                },
                clinicalImportance:
                    'Peak LA reservoir strain rate reflects LA filling rate and compliance. Reduced SRs indicates early LA dysfunction and predicts atrial fibrillation development. More sensitive than strain for detecting subclinical LA impairment.',
              ),
              _buildModernParameterCard(
                'Peak LA Conduit Strain Rate (SRe)',
                'Normal: >-1.2 s⁻¹ • Borderline: -0.8 to -1.2 s⁻¹',
                'Peak negative strain rate during early diastole',
                normalRange: [1.2, 2.5],
                currentValue: 1.8,
                unit: 's⁻¹',
                abnormalRanges: {
                  'mild': [0.8, 1.1],
                  'moderate': [0.5, 0.7],
                  'severe': [0.2, 0.4],
                },
                clinicalImportance:
                    'Peak LA conduit strain rate reflects passive LA emptying rate and LV relaxation. Reduced SRe indicates impaired LV relaxation and elevated filling pressures. Early marker of diastolic dysfunction.',
              ),
              _buildModernParameterCard(
                'Peak LA Contractile Strain Rate (SRa)',
                'Normal: >-1.5 s⁻¹ • Borderline: -1.0 to -1.5 s⁻¹',
                'Peak negative strain rate during atrial contraction',
                normalRange: [1.5, 3.0],
                currentValue: 2.1,
                unit: 's⁻¹',
                abnormalRanges: {
                  'mild': [1.0, 1.4],
                  'moderate': [0.7, 0.9],
                  'severe': [0.3, 0.6],
                },
                clinicalImportance:
                    'Peak LA contractile strain rate reflects intrinsic LA contractile function and booster pump efficiency. Reduced SRa indicates LA myopathy and predicts poor outcomes in heart failure patients.',
              ),
              _buildModernParameterCard(
                'LA Strain Rate Ratio (SRs/SRa)',
                'Normal: 1.0-1.5 • Borderline: 0.7-1.0',
                'Ratio of reservoir to contractile strain rates',
                normalRange: [1.0, 1.5],
                currentValue: 1.2,
                unit: '',
                abnormalRanges: {
                  'mild': [0.7, 0.9],
                  'moderate': [0.5, 0.6],
                  'severe': [0.2, 0.4],
                },
                clinicalImportance:
                    'LA strain rate ratio reflects balance between passive and active LA function. Altered ratio indicates LA remodeling and dysfunction. Useful for characterizing LA functional phenotypes in different diseases.',
              ),
            ],
          ),

          _buildModernSectionHeader(
            'Left Atrial Appendage Assessment',
            FontAwesomeIcons.heartPulse,
            [Color(0xFFE74C3C), Color(0xFFEC7063)],
            'laAppendage',
          ),
          _buildCollapsibleSection('laAppendage', [
            _buildModernParameterCard(
              'LAA Emptying Velocity',
              'Normal: >40 cm/s • Borderline: 25-40 cm/s',
              'Peak emptying velocity measured by pulsed Doppler',
              normalRange: [40, 80],
              currentValue: 55,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [25, 39],
                'moderate': [15, 24],
                'severe': [0, 14],
              },
              clinicalImportance:
                  'LAA emptying velocity reflects LAA contractile function and thromboembolic risk. Velocities <20 cm/s indicate high stroke risk and spontaneous echo contrast. Critical for anticoagulation decisions in atrial fibrillation.',
            ),
            _buildModernParameterCard(
              'LAA Filling Velocity',
              'Normal: >35 cm/s • Borderline: 20-35 cm/s',
              'Peak filling velocity during ventricular systole',
              normalRange: [35, 70],
              currentValue: 45,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [20, 34],
                'moderate': [10, 19],
                'severe': [0, 9],
              },
              clinicalImportance:
                  'LAA filling velocity indicates LAA compliance and connection with main LA. Reduced velocities suggest LAA dysfunction and increased thromboembolic risk. Important for stroke risk stratification.',
            ),
            _buildModernParameterCard(
              'LAA Volume',
              'Normal: 3-7 mL • Indexed: 1.5-4.0 mL/m²',
              'Measured by 3D echocardiography or biplane method',
              normalRange: [3, 7],
              currentValue: 5,
              unit: 'mL',
              abnormalRanges: {
                'mild': [7.1, 10],
                'moderate': [10.1, 15],
                'severe': [15.1, 25],
              },
              clinicalImportance:
                  'LAA volume reflects LAA remodeling and dysfunction. Enlarged LAA volume indicates increased stroke risk and poor LAA function. Important for pre-procedural planning in LAA closure procedures.',
            ),
            _buildModernParameterCard(
              'LAA Morphology Classification',
              'Chicken Wing (48%) • Windsock (19%) • Cactus (30%) • Cauliflower (3%)',
              'Based on TEE assessment of LAA shape and lobes',
              normalRange: [1, 4],
              currentValue: 1,
              unit: 'Type',
              abnormalRanges: {
                'mild': [2, 2],
                'moderate': [3, 3],
                'severe': [4, 4],
              },
              clinicalImportance:
                  'LAA morphology affects stroke risk and procedural success. Chicken Wing morphology has lowest stroke risk. Cauliflower morphology has highest stroke risk and complexity for LAA closure. Critical for pre-procedural planning and risk stratification in atrial fibrillation.',
            ),
            _buildModernParameterCard(
              'LAA Orifice Diameter',
              'Normal: 15-25 mm • Large: >25 mm',
              'Measured at LAA ostium in TEE or 3D echo',
              normalRange: [15, 25],
              currentValue: 20,
              unit: 'mm',
              abnormalRanges: {
                'mild': [25, 30],
                'moderate': [30, 35],
                'severe': [35, 45],
              },
              clinicalImportance:
                  'LAA orifice diameter determines device sizing for LAA closure procedures. Large orifices may require larger devices or multiple devices. Critical measurement for procedural planning and success.',
            ),
            _buildModernParameterCard(
              'LAA Depth',
              'Normal: 20-30 mm • Shallow: <20 mm',
              'Distance from orifice to LAA apex',
              normalRange: [20, 30],
              currentValue: 25,
              unit: 'mm',
              abnormalRanges: {
                'mild': [15, 19],
                'moderate': [10, 14],
                'severe': [5, 9],
              },
              clinicalImportance:
                  'LAA depth affects device stability and closure success. Shallow LAA (<20 mm) increases procedural complexity and risk of device embolization. Important for device selection and procedural planning.',
            ),
          ]),

          _buildModernSectionHeader(
            'LA Mechanical Dispersion',
            FontAwesomeIcons.clockRotateLeft,
            [Color(0xFF8E44AD), Color(0xFFAB7AC7)],
            'laMechanical',
          ),
          _buildCollapsibleSection('laMechanical', [
            _buildModernParameterCard(
              'LA Mechanical Dispersion',
              'Normal: <40 ms • Borderline: 40-60 ms',
              'Standard deviation of time to peak strain across LA segments',
              normalRange: [15, 40],
              currentValue: 28,
              unit: 'ms',
              abnormalRanges: {
                'mild': [40, 60],
                'moderate': [60, 80],
                'severe': [80, 150],
              },
              clinicalImportance:
                  'LA mechanical dispersion reflects LA dyssynchrony and electrical heterogeneity. Increased dispersion predicts atrial fibrillation development and recurrence after cardioversion or ablation. Early marker of LA electrical remodeling.',
            ),
            _buildModernParameterCard(
              'Time to Peak LA Strain',
              'Normal: 350-450 ms • Uniform across segments',
              'Time from QRS onset to peak positive strain',
              normalRange: [350, 450],
              currentValue: 400,
              unit: 'ms',
              abnormalRanges: {
                'mild': [450, 500],
                'moderate': [500, 600],
                'severe': [600, 800],
              },
              clinicalImportance:
                  'Time to peak strain reflects LA activation timing and synchrony. Prolonged or variable timing indicates LA conduction abnormalities. Useful for assessing LA electrical function and predicting arrhythmia risk.',
            ),
          ]),

          _buildModernSectionHeader(
            'LA Synchrony Assessment',
            FontAwesomeIcons.waveSquare,
            [Color(0xFF1ABC9C), Color(0xFF48C9B0)],
            'laSynchrony',
          ),
          _buildCollapsibleSection('laSynchrony', [
            _buildModernParameterCard(
              'Intra-LA Dyssynchrony Index',
              'Normal: <50 ms • Borderline: 50-80 ms',
              'Maximum time difference between LA segments to peak strain',
              normalRange: [20, 50],
              currentValue: 35,
              unit: 'ms',
              abnormalRanges: {
                'mild': [50, 80],
                'moderate': [80, 120],
                'severe': [120, 200],
              },
              clinicalImportance:
                  'Intra-LA dyssynchrony reflects electrical heterogeneity and conduction abnormalities within the LA. Increased dyssynchrony predicts atrial fibrillation development, recurrence after cardioversion, and poor response to ablation therapy.',
            ),
            _buildModernParameterCard(
              'LA-LV Coupling Index',
              'Normal: 0.8-1.2 • Borderline: 0.6-0.8',
              'Ratio of LA to LV peak strain timing',
              normalRange: [0.8, 1.2],
              currentValue: 1.0,
              unit: '',
              abnormalRanges: {
                'mild': [0.6, 0.79],
                'moderate': [0.4, 0.59],
                'severe': [0.2, 0.39],
              },
              clinicalImportance:
                  'LA-LV coupling reflects temporal relationship between atrial and ventricular function. Abnormal coupling indicates atrioventricular dyssynchrony and predicts poor hemodynamic efficiency and exercise intolerance.',
            ),
            _buildModernParameterCard(
              'LA Electrical Activation Time',
              'Normal: <120 ms • Borderline: 120-150 ms',
              'Total time for electrical activation across LA',
              normalRange: [80, 120],
              currentValue: 100,
              unit: 'ms',
              abnormalRanges: {
                'mild': [120, 150],
                'moderate': [150, 200],
                'severe': [200, 300],
              },
              clinicalImportance:
                  'LA electrical activation time reflects conduction velocity and substrate for arrhythmias. Prolonged activation indicates slow conduction and increased arrhythmia susceptibility. Critical for ablation planning.',
            ),
            _buildModernParameterCard(
              'LA Conduction Velocity',
              'Normal: >0.8 m/s • Borderline: 0.6-0.8 m/s',
              'Speed of electrical propagation across LA',
              normalRange: [0.8, 1.5],
              currentValue: 1.1,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [0.6, 0.79],
                'moderate': [0.4, 0.59],
                'severe': [0.2, 0.39],
              },
              clinicalImportance:
                  'LA conduction velocity reflects tissue properties and fibrosis. Reduced velocity indicates electrical remodeling and increased arrhythmia risk. Important for understanding AF mechanisms and ablation outcomes.',
            ),
            _buildModernParameterCard(
              'LA Synchrony Score',
              'Normal: >80% • Borderline: 60-80%',
              'Percentage of LA segments with synchronized contraction',
              normalRange: [80, 100],
              currentValue: 90,
              unit: '%',
              abnormalRanges: {
                'mild': [60, 79],
                'moderate': [40, 59],
                'severe': [20, 39],
              },
              clinicalImportance:
                  'LA synchrony score quantifies overall LA mechanical coordination. Reduced synchrony indicates advanced LA remodeling and dysfunction. Strong predictor of atrial fibrillation burden and response to rhythm control therapy.',
            ),
            _buildModernParameterCard(
              'Inter-Atrial Conduction Time',
              'Normal: <120 ms • Borderline: 120-140 ms',
              'Time from RA to LA electrical activation',
              normalRange: [80, 120],
              currentValue: 100,
              unit: 'ms',
              abnormalRanges: {
                'mild': [120, 140],
                'moderate': [140, 180],
                'severe': [180, 250],
              },
              clinicalImportance:
                  'Inter-atrial conduction time reflects Bachmann bundle function and atrial conduction. Prolonged conduction indicates inter-atrial block and predicts atrial fibrillation development and stroke risk.',
            ),
          ]),

          _buildModernSectionHeader(
            'LA Compliance Assessment',
            FontAwesomeIcons.scaleBalanced,
            [Color(0xFF16A085), Color(0xFF48C9B0)],
            'laCompliance',
          ),
          _buildCollapsibleSection('laCompliance', [
            _buildModernParameterCard(
              'LA Compliance',
              'Normal: >2.5 mL/mmHg • Borderline: 1.5-2.5 mL/mmHg',
              'Calculated as ΔLA volume / ΔLA pressure',
              normalRange: [2.5, 6.0],
              currentValue: 3.8,
              unit: 'mL/mmHg',
              abnormalRanges: {
                'mild': [1.5, 2.4],
                'moderate': [1.0, 1.4],
                'severe': [0.3, 0.9],
              },
              clinicalImportance:
                  'LA compliance reflects LA chamber stiffness and distensibility. Reduced compliance indicates LA fibrosis and diastolic dysfunction. Strong predictor of heart failure progression and exercise intolerance.',
            ),
            _buildModernParameterCard(
              'LA Pressure-Volume Relationship',
              'Normal: Linear relationship • Steep slope indicates stiffness',
              'Slope of pressure-volume curve during filling',
              normalRange: [0.1, 0.3],
              currentValue: 0.2,
              unit: 'mmHg/mL',
              abnormalRanges: {
                'mild': [0.3, 0.5],
                'moderate': [0.5, 0.8],
                'severe': [0.8, 2.0],
              },
              clinicalImportance:
                  'P-V relationship slope reflects LA stiffness and compliance. Steep slope indicates reduced compliance and elevated filling pressures. Important for understanding diastolic dysfunction mechanisms.',
            ),
          ]),

          _buildModernSectionHeader(
            'LA Phasic Volumes',
            FontAwesomeIcons.chartArea,
            [Color(0xFFF39C12), Color(0xFFF7DC6F)],
            'laPhasic',
          ),
          _buildCollapsibleSection('laPhasic', [
            _buildModernParameterCard(
              'LA Maximum Volume (LAVmax)',
              'Women: 22±6 mL/m² • Men: 22±6 mL/m²',
              'Volume at end-systole (before mitral valve opening)',
              normalRange: [16, 28],
              currentValue: 22,
              unit: 'mL/m²',
              abnormalRanges: {
                'mild': [29, 33],
                'moderate': [34, 39],
                'severe': [40, 80],
              },
              clinicalImportance:
                  'LAVmax reflects total LA filling capacity and reservoir function. Enlarged LAVmax indicates chronic volume or pressure overload. Strong predictor of cardiovascular events and mortality.',
            ),
            _buildModernParameterCard(
              'LA Minimum Volume (LAVmin)',
              'Women: 9±4 mL/m² • Men: 9±4 mL/m²',
              'Volume at end-diastole (after atrial contraction)',
              normalRange: [5, 13],
              currentValue: 9,
              unit: 'mL/m²',
              abnormalRanges: {
                'mild': [14, 18],
                'moderate': [19, 25],
                'severe': [26, 50],
              },
              clinicalImportance:
                  'LAVmin reflects residual LA volume after emptying. Increased LAVmin indicates impaired LA emptying and dysfunction. Correlates with LA stiffness and filling pressures.',
            ),
            _buildModernParameterCard(
              'LA Pre-A Volume (LAVpreA)',
              'Women: 13±5 mL/m² • Men: 13±5 mL/m²',
              'Volume before atrial contraction (end of passive filling)',
              normalRange: [8, 18],
              currentValue: 13,
              unit: 'mL/m²',
              abnormalRanges: {
                'mild': [19, 23],
                'moderate': [24, 30],
                'severe': [31, 60],
              },
              clinicalImportance:
                  'LAVpreA reflects passive LA emptying and LV relaxation. Increased values indicate impaired LV relaxation and elevated filling pressures. Important for diastolic function assessment.',
            ),
            _buildModernParameterCard(
              'LA Expansion Index',
              'Normal: >150% • Borderline: 100-150%',
              'Calculated as (LAVmax - LAVmin) / LAVmin × 100',
              normalRange: [150, 300],
              currentValue: 200,
              unit: '%',
              abnormalRanges: {
                'mild': [100, 149],
                'moderate': [50, 99],
                'severe': [0, 49],
              },
              clinicalImportance:
                  'LA expansion index reflects LA distensibility and compliance. Reduced values indicate LA stiffness and dysfunction. Correlates with diastolic dysfunction severity and prognosis.',
            ),
          ]),

          _buildModernSectionHeader(
            'LA Ejection Force Assessment',
            FontAwesomeIcons.boltLightning,
            [Color(0xFF9C27B0), Color(0xFFBA68C8)],
            'laEjection',
          ),
          _buildCollapsibleSection('laEjection', [
            _buildModernParameterCard(
              'LA Ejection Force',
              'Normal: >15 kdyn • Borderline: 10-15 kdyn',
              'Calculated as 0.5 × ρ × (mitral A velocity)² × mitral area',
              normalRange: [15, 35],
              currentValue: 22,
              unit: 'kdyn',
              abnormalRanges: {
                'mild': [10, 14],
                'moderate': [5, 9],
                'severe': [0, 4],
              },
              clinicalImportance:
                  'LA ejection force reflects LA contractile strength and contribution to LV filling. Reduced force indicates LA dysfunction and poor atrial kick. Important for assessing LA contribution to cardiac output.',
            ),
            _buildModernParameterCard(
              'LA Kinetic Energy',
              'Normal: >3000 μJ • Borderline: 2000-3000 μJ',
              'Calculated as 0.5 × LA mass × (peak A velocity)²',
              normalRange: [3000, 8000],
              currentValue: 4500,
              unit: 'μJ',
              abnormalRanges: {
                'mild': [2000, 2999],
                'moderate': [1000, 1999],
                'severe': [0, 999],
              },
              clinicalImportance:
                  'LA kinetic energy reflects total energy generated by LA contraction. Reduced energy indicates impaired LA contractile function. Correlates with exercise capacity and heart failure symptoms.',
            ),
            _buildModernParameterCard(
              'LA Work Index',
              'Normal: >15% • Borderline: 10-15%',
              'Ratio of LA stroke work to total LV stroke work',
              normalRange: [15, 25],
              currentValue: 18,
              unit: '%',
              abnormalRanges: {
                'mild': [10, 14],
                'moderate': [5, 9],
                'severe': [0, 4],
              },
              clinicalImportance:
                  'LA work index reflects LA contribution to total cardiac work. Reduced index indicates decreased LA contribution to LV filling. Important for understanding hemodynamic compensation mechanisms.',
            ),
            _buildModernParameterCard(
              'LA Power Output',
              'Normal: >0.5 mW • Borderline: 0.3-0.5 mW',
              'Rate of LA energy generation during contraction',
              normalRange: [0.5, 1.5],
              currentValue: 0.8,
              unit: 'mW',
              abnormalRanges: {
                'mild': [0.3, 0.49],
                'moderate': [0.1, 0.29],
                'severe': [0.0, 0.09],
              },
              clinicalImportance:
                  'LA power output reflects instantaneous LA contractile performance. Reduced power indicates LA dysfunction and impaired hemodynamic contribution. Useful for assessing LA reserve function.',
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildRightVentricleContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernSectionHeader(
            'Right Ventricular Dimensions',
            FontAwesomeIcons.ruler,
            [Color(0xFF4ECDC4), Color(0xFF6EE7E0)],
            'rvDimensions',
          ),
          _buildCollapsibleSection('rvDimensions', [
            _buildModernParameterCard(
              'RV Basal Diameter',
              'Normal: <4.2 cm • Abnormal: >4.2 cm (ASE 2015)',
              'Measured in apical 4-chamber view at end-diastole',
              normalRange: [2.5, 4.2],
              currentValue: 3.5,
              unit: 'cm',
              abnormalRanges: {
                'mildly_abnormal': [4.2, 4.7],
                'moderately_abnormal': [4.8, 5.5],
                'severely_abnormal': [5.6, 8.0],
              },
              clinicalImportance:
                  'RV basal diameter reflects RV size per ASE 2015 guidelines. Enlarged RV indicates pulmonary hypertension, tricuspid regurgitation, or RV cardiomyopathy.',
            ),
            _buildModernParameterCard(
              'RV Mid-Cavity Diameter',
              'Normal: <3.5 cm • Abnormal: >3.5 cm (ASE 2015)',
              'Measured in apical 4-chamber view at mid-level',
              normalRange: [2.0, 3.5],
              currentValue: 2.8,
              unit: 'cm',
              abnormalRanges: {
                'mildly_abnormal': [3.5, 4.0],
                'moderately_abnormal': [4.1, 4.8],
                'severely_abnormal': [4.9, 7.0],
              },
              clinicalImportance:
                  'RV mid-cavity diameter provides additional assessment of RV size per ASE 2015 guidelines. Useful for detecting regional RV enlargement.',
            ),
            _buildModernParameterCard(
              'RV Longitudinal Diameter',
              'Normal: <8.6 cm • Abnormal: >8.6 cm (ASE 2015)',
              'Measured from tricuspid annulus to RV apex',
              normalRange: [6.0, 8.6],
              currentValue: 7.5,
              unit: 'cm',
              abnormalRanges: {
                'mildly_abnormal': [8.6, 9.1],
                'moderately_abnormal': [9.2, 10.0],
                'severely_abnormal': [10.1, 12.0],
              },
              clinicalImportance:
                  'RV longitudinal diameter reflects RV length per ASE 2015 guidelines. Important for comprehensive RV size assessment.',
            ),
            _buildModernParameterCard(
              'RV Wall Thickness',
              'Normal: <0.5 cm • Abnormal: >0.5 cm (ASE 2015)',
              'Measured at end-diastole in subcostal view',
              normalRange: [0.2, 0.5],
              currentValue: 0.4,
              unit: 'cm',
              abnormalRanges: {
                'abnormal': [0.5, 2.0],
              },
              clinicalImportance:
                  'RV wall thickness indicates RV hypertrophy per ASE 2015 guidelines. Increased thickness suggests pulmonary hypertension or pressure overload.',
            ),
            _buildModernParameterCard(
              'RV Outflow Tract (RVOT) Diameter',
              'Normal: 2.5-2.9 cm • Mild: 3.0-3.5 cm',
              'Measured in parasternal short axis view',
              normalRange: [2.5, 2.9],
              currentValue: 2.7,
              unit: 'cm',
              abnormalRanges: {
                'mild': [3.0, 3.5],
                'moderate': [3.6, 4.0],
                'severe': [4.1, 5.0],
              },
              clinicalImportance:
                  'RVOT diameter reflects RV outflow tract size. Enlarged RVOT may indicate pulmonary hypertension or RV volume overload. Important for assessing RV remodeling patterns.',
            ),
          ]),

          _buildModernSectionHeader(
            'Right Ventricular Volumes',
            FontAwesomeIcons.cube,
            [Color(0xFF8E44AD), Color(0xFFAB7AC7)],
            'rvVolumes',
          ),
          _buildCollapsibleSection('rvVolumes', [
            _buildModernParameterCard(
              'RV End-Diastolic Volume (RVEDV)',
              'Normal: 89±23 mL/m² • Mild: 110-130 mL/m²',
              'BSA indexed volume using 3D or biplane method',
              normalRange: [66, 112],
              currentValue: 85,
              unit: 'mL/m²',
              abnormalRanges: {
                'mild': [110, 130],
                'moderate': [131, 160],
                'severe': [161, 250],
              },
              clinicalImportance:
                  'RVEDV reflects RV preload and chamber size. Increased values indicate volume overload or RV dysfunction. Essential for calculating RV ejection fraction.',
            ),
            _buildModernParameterCard(
              'RV End-Systolic Volume (RVESV)',
              'Normal: 45±13 mL/m² • Mild: 55-70 mL/m²',
              'BSA indexed volume using 3D or biplane method',
              normalRange: [32, 58],
              currentValue: 42,
              unit: 'mL/m²',
              abnormalRanges: {
                'mild': [55, 70],
                'moderate': [71, 90],
                'severe': [91, 150],
              },
              clinicalImportance:
                  'RVESV reflects RV contractility and systolic function. Increased values indicate impaired RV systolic function. Critical for calculating RV ejection fraction.',
            ),
            _buildModernParameterCard(
              'RV Ejection Fraction (RVEF)',
              'Normal: >45% • Borderline: 35-45%',
              'Calculated from RV volumes (RVEDV-RVESV)/RVEDV',
              normalRange: [45, 70],
              currentValue: 55,
              unit: '%',
              abnormalRanges: {
                'mild': [35, 44],
                'moderate': [25, 34],
                'severe': [0, 24],
              },
              clinicalImportance:
                  'RVEF is the gold standard for RV systolic function when volumes are available. Reduced RVEF indicates RV dysfunction and poor prognosis in various conditions.',
            ),
          ]),

          _buildModernSectionHeader(
            'Right Ventricular Function',
            FontAwesomeIcons.chartLine,
            [Color(0xFF9B59B6), Color(0xFFBB7FD1)],
            'rvFunction',
          ),
          _buildCollapsibleSection('rvFunction', [
            _buildModernParameterCard(
              'RV Fractional Area Change (FAC)',
              'Normal: >35% • Abnormal: <35% (ASE 2015)',
              'Most important parameter for RV systolic function',
              normalRange: [35, 60],
              currentValue: 45,
              unit: '%',
              abnormalRanges: {
                'abnormal': [0, 34],
              },
              clinicalImportance:
                  'RV FAC is the primary parameter for assessing RV systolic function per ASE 2015 guidelines. Reduced FAC indicates RV dysfunction.',
            ),
            _buildModernParameterCard(
              'TAPSE (Tricuspid Annular Plane Systolic Excursion)',
              'Normal: >1.7 cm • Abnormal: <1.7 cm (ASE 2015)',
              'Simple measure of RV longitudinal function',
              normalRange: [1.7, 3.0],
              currentValue: 2.2,
              unit: 'cm',
              abnormalRanges: {
                'abnormal': [0.5, 1.6],
              },
              clinicalImportance:
                  'TAPSE reflects RV longitudinal systolic function per ASE 2015 guidelines. Easy to measure and highly reproducible. Reduced TAPSE indicates RV dysfunction.',
            ),
            _buildModernParameterCard(
              'RV S\' (Tissue Doppler)',
              'Normal: >9.5 cm/s • Abnormal: <9.5 cm/s (ASE 2015)',
              'Tissue Doppler velocity at tricuspid annulus',
              normalRange: [9.5, 15.0],
              currentValue: 12.0,
              unit: 'cm/s',
              abnormalRanges: {
                'abnormal': [3.0, 9.4],
              },
              clinicalImportance:
                  'RV S\' velocity reflects RV longitudinal systolic function per ASE 2015 guidelines. More sensitive than TAPSE for detecting early RV dysfunction.',
            ),
          ]),

          _buildModernSectionHeader(
            'Right Ventricular Pressure Assessment',
            FontAwesomeIcons.waveSquare,
            [Color(0xFFFF9500), Color(0xFFFFB347)],
            'rvPressure',
          ),
          _buildCollapsibleSection('rvPressure', [
            _buildModernParameterCard(
              'RV Systolic Pressure (RVSP)',
              'Normal: 15-30 mmHg • Mild PH: 31-50 mmHg',
              'Estimated from tricuspid regurgitation velocity',
              normalRange: [15, 30],
              currentValue: 25,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [31, 50],
                'moderate': [51, 70],
                'severe': [71, 120],
              },
              clinicalImportance:
                  'RVSP estimates pulmonary artery systolic pressure. Elevated RVSP indicates pulmonary hypertension. Critical for diagnosing and monitoring pulmonary vascular disease.',
            ),
            _buildModernParameterCard(
              'Pulmonary Artery Systolic Pressure (PASP)',
              'Normal: 15-30 mmHg • Mild PH: 31-50 mmHg',
              'Calculated as RVSP + RA pressure (4 × TR velocity² + RA pressure)',
              normalRange: [15, 30],
              currentValue: 28,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [31, 50],
                'moderate': [51, 70],
                'severe': [71, 120],
              },
              clinicalImportance:
                  'PASP is the gold standard for diagnosing pulmonary hypertension. PASP >35 mmHg indicates pulmonary hypertension. Critical for screening, diagnosis, and monitoring of pulmonary vascular disease. More accurate than RVSP alone.',
            ),
            _buildModernParameterCard(
              'Tricuspid Regurgitation Velocity (TR Vmax)',
              'Normal: <2.8 m/s • Mild PH: 2.8-3.4 m/s',
              'Peak velocity used to calculate PASP and RVSP',
              normalRange: [2.0, 2.8],
              currentValue: 2.5,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [2.8, 3.4],
                'moderate': [3.5, 4.0],
                'severe': [4.1, 6.0],
              },
              clinicalImportance:
                  'TR velocity is the primary measurement for estimating pulmonary pressures. TR Vmax >2.8 m/s suggests pulmonary hypertension. Essential for PASP calculation using simplified Bernoulli equation.',
            ),
            _buildModernParameterCard(
              'RV dP/dt',
              'Normal: >400 mmHg/s • Borderline: 300-400 mmHg/s',
              'Rate of RV pressure rise from tricuspid regurgitation',
              normalRange: [400, 800],
              currentValue: 550,
              unit: 'mmHg/s',
              abnormalRanges: {
                'mild': [300, 399],
                'moderate': [200, 299],
                'severe': [100, 199],
              },
              clinicalImportance:
                  'RV dP/dt reflects RV contractility independent of preload and afterload. Reduced RV dP/dt indicates impaired RV contractile function.',
            ),
          ]),

          _buildModernSectionHeader(
            'RV Diastolic Function',
            FontAwesomeIcons.waveSquare,
            [Color(0xFF16A085), Color(0xFF48C9B0)],
            'rvDiastolic',
          ),
          _buildCollapsibleSection('rvDiastolic', [
            _buildModernParameterCard(
              'Tricuspid Inflow E/A Ratio',
              'Normal: 0.8-2.1 • Age-dependent parameter',
              'Ratio of early to late diastolic filling velocities',
              normalRange: [0.8, 2.1],
              currentValue: 1.4,
              unit: '',
              abnormalRanges: {
                'mild': [0.6, 0.79],
                'moderate': [2.2, 3.0],
                'severe': [0.0, 0.59],
              },
              clinicalImportance:
                  'Tricuspid E/A ratio reflects RV diastolic filling pattern. Abnormal ratios indicate RV diastolic dysfunction. Pattern similar to mitral inflow but less well-studied.',
            ),
            _buildModernParameterCard(
              'Tricuspid E Deceleration Time',
              'Normal: 120-200 ms • Age-dependent',
              'Time for E-wave velocity to decrease to baseline',
              normalRange: [120, 200],
              currentValue: 160,
              unit: 'ms',
              abnormalRanges: {
                'mild': [201, 250],
                'moderate': [100, 119],
                'severe': [50, 99],
              },
              clinicalImportance:
                  'Tricuspid DT reflects RV compliance and filling pressures. Prolonged DT indicates impaired RV relaxation. Shortened DT suggests elevated filling pressures.',
            ),
            _buildModernParameterCard(
              'RV e\' (Tissue Doppler)',
              'Normal: >7 cm/s • Borderline: 5-7 cm/s',
              'Early diastolic tissue velocity at tricuspid annulus',
              normalRange: [7, 15],
              currentValue: 10,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [5, 6],
                'moderate': [3, 4],
                'severe': [0, 2],
              },
              clinicalImportance:
                  'RV e\' velocity reflects RV relaxation. Reduced e\' indicates impaired RV diastolic function. Less validated than LV e\' but clinically useful.',
            ),
            _buildModernParameterCard(
              'RV E/e\' Ratio',
              'Normal: <6 • Borderline: 6-10',
              'RV filling pressures estimation (similar to LV)',
              normalRange: [3, 6],
              currentValue: 4.5,
              unit: '',
              abnormalRanges: {
                'mild': [6, 10],
                'moderate': [11, 15],
                'severe': [16, 25],
              },
              clinicalImportance:
                  'RV E/e\' ratio estimates RV filling pressures. Elevated values indicate increased RV end-diastolic pressure and diastolic dysfunction. Less validated than LV E/e\' but clinically useful.',
            ),
            _buildModernParameterCard(
              'RV Isovolumic Relaxation Time (IVRT)',
              'Normal: 50-100 ms • Prolonged: >100 ms',
              'Time from tricuspid closure to opening',
              normalRange: [50, 100],
              currentValue: 75,
              unit: 'ms',
              abnormalRanges: {
                'mild': [100, 120],
                'moderate': [121, 150],
                'severe': [151, 200],
              },
              clinicalImportance:
                  'RV IVRT reflects RV relaxation properties. Prolonged IVRT indicates impaired RV relaxation. Shortened IVRT may indicate elevated filling pressures.',
            ),
            _buildModernParameterCard(
              'RV Filling Pressure Estimation',
              'Normal: <8 mmHg • Elevated: >15 mmHg',
              'Estimated from tricuspid inflow and tissue Doppler',
              normalRange: [3, 8],
              currentValue: 6,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [8, 15],
                'moderate': [16, 20],
                'severe': [21, 30],
              },
              clinicalImportance:
                  'RV filling pressure estimation helps assess RV diastolic function and volume status. Elevated pressures indicate RV dysfunction and poor prognosis.',
            ),
          ]),

          _buildModernSectionHeader(
            'Pulmonary Artery Assessment',
            FontAwesomeIcons.lungs,
            [Color(0xFFF39C12), Color(0xFFF7DC6F)],
            'rvPulmonary',
          ),
          _buildCollapsibleSection('rvPulmonary', [
            _buildModernParameterCard(
              'Main Pulmonary Artery Diameter',
              'Normal: <2.9 cm • Mild: 2.9-3.3 cm',
              'Measured in parasternal short axis view',
              normalRange: [2.0, 2.9],
              currentValue: 2.5,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.9, 3.3],
                'moderate': [3.4, 4.0],
                'severe': [4.1, 6.0],
              },
              clinicalImportance:
                  'Main PA diameter reflects pulmonary vascular pressures. Enlarged PA suggests pulmonary hypertension. PA/Aorta ratio >1.0 indicates significant PA enlargement.',
            ),
            _buildModernParameterCard(
              'Pulmonary Acceleration Time (PAT)',
              'Normal: >105 ms • Mild PH: 80-105 ms',
              'Time from onset to peak of pulmonary flow',
              normalRange: [105, 150],
              currentValue: 125,
              unit: 'ms',
              abnormalRanges: {
                'mild': [80, 104],
                'moderate': [60, 79],
                'severe': [30, 59],
              },
              clinicalImportance:
                  'PAT reflects pulmonary vascular resistance. Shortened PAT indicates elevated pulmonary pressures. PAT <60 ms suggests severe pulmonary hypertension.',
            ),
            _buildModernParameterCard(
              'Pulmonary Regurgitation Velocity',
              'Normal: <2.2 m/s • Mild PH: 2.2-2.8 m/s',
              'Peak velocity of pulmonary regurgitation jet',
              normalRange: [1.0, 2.2],
              currentValue: 1.8,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [2.2, 2.8],
                'moderate': [2.9, 3.5],
                'severe': [3.6, 5.0],
              },
              clinicalImportance:
                  'PR velocity estimates PA diastolic pressure. Elevated PR velocity indicates pulmonary hypertension. Used to calculate PA diastolic pressure.',
            ),
          ]),

          _buildModernSectionHeader(
            'Advanced RV Parameters',
            FontAwesomeIcons.heartCircleBolt,
            [Color(0xFFE74C3C), Color(0xFFEC7063)],
            'rvAdvanced',
          ),
          _buildCollapsibleSection('rvAdvanced', [
            _buildModernParameterCard(
              'RV Global Longitudinal Strain',
              'Normal: >-20% • Borderline: -15% to -20%',
              'Most sensitive parameter for RV dysfunction',
              normalRange: [20, 30],
              currentValue: 25,
              unit: '%',
              abnormalRanges: {
                'mild': [15, 19],
                'moderate': [10, 14],
                'severe': [5, 9],
              },
              clinicalImportance:
                  'RV GLS is the most sensitive parameter for detecting early RV dysfunction. Abnormal RV strain may precede changes in conventional parameters. Critical for early detection of RV impairment.',
            ),
            _buildModernParameterCard(
              'RV Myocardial Performance Index (MPI)',
              'Normal: <0.4 • Borderline: 0.4-0.5',
              'Combined measure of RV systolic and diastolic function',
              normalRange: [0.2, 0.4],
              currentValue: 0.3,
              unit: '',
              abnormalRanges: {
                'mild': [0.4, 0.6],
                'moderate': [0.6, 0.8],
                'severe': [0.8, 1.5],
              },
              clinicalImportance:
                  'RV MPI combines systolic and diastolic function assessment. Elevated MPI indicates global RV dysfunction. Independent predictor of outcomes in pulmonary hypertension.',
            ),
          ]),

          _buildModernSectionHeader(
            'RV-PA Coupling Assessment',
            FontAwesomeIcons.link,
            [Color(0xFF9C27B0), Color(0xFFBA68C8)],
            'rvPACoupling',
          ),
          _buildCollapsibleSection('rvPACoupling', [
            _buildModernParameterCard(
              'TAPSE/PASP Ratio',
              'Normal: >0.36 mm/mmHg • Borderline: 0.25-0.36',
              'Reflects RV efficiency and adaptation to afterload',
              normalRange: [0.36, 1.0],
              currentValue: 0.45,
              unit: 'mm/mmHg',
              abnormalRanges: {
                'mild': [0.25, 0.35],
                'moderate': [0.15, 0.24],
                'severe': [0.05, 0.14],
              },
              clinicalImportance:
                  'TAPSE/PASP ratio reflects RV-arterial coupling and RV efficiency. Values <0.36 indicate RV-PA uncoupling and poor adaptation to increased afterload. Strong predictor of outcomes in pulmonary hypertension.',
            ),
            _buildModernParameterCard(
              'RV Stroke Volume/End-Systolic Volume',
              'Normal: >1.5 • Borderline: 1.0-1.5',
              'RV-arterial coupling index reflecting contractile efficiency',
              normalRange: [1.5, 3.0],
              currentValue: 2.0,
              unit: '',
              abnormalRanges: {
                'mild': [1.0, 1.4],
                'moderate': [0.7, 0.9],
                'severe': [0.3, 0.6],
              },
              clinicalImportance:
                  'RV SV/ESV ratio reflects optimal RV-arterial coupling. Reduced values indicate inefficient RV performance and poor adaptation to afterload. Important for assessing RV reserve and prognosis.',
            ),
            _buildModernParameterCard(
              'RV Arterial Elastance (Ea)',
              'Normal: 0.5-1.5 mmHg/mL • Elevated: >2.0',
              'Reflects afterload faced by RV (PASP/SV)',
              normalRange: [0.5, 1.5],
              currentValue: 1.0,
              unit: 'mmHg/mL',
              abnormalRanges: {
                'mild': [1.5, 2.0],
                'moderate': [2.1, 3.0],
                'severe': [3.1, 6.0],
              },
              clinicalImportance:
                  'RV arterial elastance quantifies RV afterload. Elevated Ea indicates increased pulmonary vascular resistance and afterload mismatch. Critical for understanding RV-PA coupling.',
            ),
          ]),

          _buildModernSectionHeader(
            'RV Strain Rate Parameters',
            FontAwesomeIcons.gaugeHigh,
            [Color(0xFFFF5722), Color(0xFFFF8A65)],
            'rvStrainRate',
          ),
          _buildCollapsibleSection('rvStrainRate', [
            _buildModernParameterCard(
              'RV Free Wall Strain Rate',
              'Normal: >-1.5 s⁻¹ • Borderline: -1.0 to -1.5 s⁻¹',
              'Peak systolic strain rate of RV free wall',
              normalRange: [1.5, 3.0],
              currentValue: 2.2,
              unit: 's⁻¹',
              abnormalRanges: {
                'mild': [1.0, 1.4],
                'moderate': [0.7, 0.9],
                'severe': [0.3, 0.6],
              },
              clinicalImportance:
                  'RV free wall strain rate is more sensitive than strain for detecting early RV dysfunction. Reduced values indicate impaired RV contractility and predict outcomes in pulmonary hypertension.',
            ),
            _buildModernParameterCard(
              'RV Septal Strain Rate',
              'Normal: >-1.2 s⁻¹ • Borderline: -0.8 to -1.2 s⁻¹',
              'Peak systolic strain rate of interventricular septum',
              normalRange: [1.2, 2.5],
              currentValue: 1.8,
              unit: 's⁻¹',
              abnormalRanges: {
                'mild': [0.8, 1.1],
                'moderate': [0.5, 0.7],
                'severe': [0.2, 0.4],
              },
              clinicalImportance:
                  'RV septal strain rate reflects interventricular septum contribution to RV function. Reduced values indicate septal dysfunction and RV-LV interaction abnormalities.',
            ),
            _buildModernParameterCard(
              'RV Global Strain Rate',
              'Normal: >-1.3 s⁻¹ • Borderline: -0.9 to -1.3 s⁻¹',
              'Average strain rate across all RV segments',
              normalRange: [1.3, 2.8],
              currentValue: 2.0,
              unit: 's⁻¹',
              abnormalRanges: {
                'mild': [0.9, 1.2],
                'moderate': [0.6, 0.8],
                'severe': [0.2, 0.5],
              },
              clinicalImportance:
                  'RV global strain rate provides comprehensive assessment of RV contractile function. More sensitive than conventional parameters for detecting subclinical RV dysfunction.',
            ),
          ]),

          _buildModernSectionHeader(
            'RV Synchrony Assessment',
            FontAwesomeIcons.waveSquare,
            [Color(0xFF00BCD4), Color(0xFF4DD0E1)],
            'rvSynchrony',
          ),
          _buildCollapsibleSection('rvSynchrony', [
            _buildModernParameterCard(
              'RV Mechanical Dyssynchrony',
              'Normal: <50 ms • Borderline: 50-80 ms',
              'Time difference between RV segments to peak strain',
              normalRange: [20, 50],
              currentValue: 35,
              unit: 'ms',
              abnormalRanges: {
                'mild': [50, 80],
                'moderate': [80, 120],
                'severe': [120, 200],
              },
              clinicalImportance:
                  'RV mechanical dyssynchrony reflects electrical and mechanical heterogeneity. Increased dyssynchrony indicates RV dysfunction and predicts poor outcomes in pulmonary hypertension.',
            ),
            _buildModernParameterCard(
              'RV-LV Interaction Index',
              'Normal: 0.8-1.2 • Abnormal: <0.6 or >1.4',
              'Reflects interventricular dependence and septal shift',
              normalRange: [0.8, 1.2],
              currentValue: 1.0,
              unit: '',
              abnormalRanges: {
                'mild': [0.6, 0.79],
                'moderate': [0.4, 0.59],
                'severe': [0.2, 0.39],
              },
              clinicalImportance:
                  'RV-LV interaction index reflects ventricular interdependence. Abnormal values indicate septal shift and impaired biventricular function. Important in RV pressure/volume overload.',
            ),
            _buildModernParameterCard(
              'RV Electrical-Mechanical Coupling',
              'Normal: <120 ms • Prolonged: >150 ms',
              'Time from QRS onset to peak RV strain',
              normalRange: [80, 120],
              currentValue: 100,
              unit: 'ms',
              abnormalRanges: {
                'mild': [120, 150],
                'moderate': [150, 200],
                'severe': [200, 300],
              },
              clinicalImportance:
                  'RV electrical-mechanical coupling reflects conduction and contractile efficiency. Prolonged coupling indicates electrical remodeling and mechanical dysfunction.',
            ),
          ]),

          _buildModernSectionHeader(
            'Pulmonary Vascular Assessment',
            FontAwesomeIcons.lungs,
            [Color(0xFF607D8B), Color(0xFF90A4AE)],
            'rvPulmonaryVascular',
          ),
          _buildCollapsibleSection('rvPulmonaryVascular', [
            _buildModernParameterCard(
              'Pulmonary Artery Compliance',
              'Normal: >2.0 mL/mmHg • Reduced: <1.5 mL/mmHg',
              'PA distensibility (Stroke Volume / Pulse Pressure)',
              normalRange: [2.0, 4.0],
              currentValue: 2.8,
              unit: 'mL/mmHg',
              abnormalRanges: {
                'mild': [1.5, 1.9],
                'moderate': [1.0, 1.4],
                'severe': [0.5, 0.9],
              },
              clinicalImportance:
                  'PA compliance reflects pulmonary vascular stiffness. Reduced compliance indicates pulmonary vascular remodeling and increased RV afterload. Strong predictor of outcomes in PH.',
            ),
            _buildModernParameterCard(
              'Pulmonary Artery Pulsatility Index',
              'Normal: <1.5 • Elevated: >2.5',
              'PA stiffness assessment (Pulse Pressure / Mean PA Pressure)',
              normalRange: [0.8, 1.5],
              currentValue: 1.2,
              unit: '',
              abnormalRanges: {
                'mild': [1.5, 2.5],
                'moderate': [2.5, 3.5],
                'severe': [3.5, 6.0],
              },
              clinicalImportance:
                  'PA pulsatility index reflects pulmonary vascular stiffness and wave reflections. Elevated values indicate advanced pulmonary vascular disease and poor prognosis.',
            ),
            _buildModernParameterCard(
              'PA Systolic/Diastolic Ratio',
              'Normal: 2.0-3.0 • Elevated: >4.0',
              'Vascular resistance marker reflecting PA pressure profile',
              normalRange: [2.0, 3.0],
              currentValue: 2.5,
              unit: '',
              abnormalRanges: {
                'mild': [3.0, 4.0],
                'moderate': [4.0, 5.0],
                'severe': [5.0, 8.0],
              },
              clinicalImportance:
                  'PA systolic/diastolic ratio reflects pulmonary vascular resistance and compliance. Elevated ratios indicate increased afterload and vascular remodeling.',
            ),
          ]),

          _buildModernSectionHeader(
            'Advanced Hemodynamic Calculations',
            FontAwesomeIcons.calculator,
            [Color(0xFF795548), Color(0xFFA1887F)],
            'rvHemodynamics',
          ),
          _buildCollapsibleSection('rvHemodynamics', [
            _buildModernParameterCard(
              'Pulmonary Artery Mean Pressure (PAMP)',
              'Normal: 8-20 mmHg • Mild PH: 21-30 mmHg',
              'Calculated from PAT or PR velocity (0.61 × PASP + 2)',
              normalRange: [8, 20],
              currentValue: 15,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [21, 30],
                'moderate': [31, 45],
                'severe': [46, 80],
              },
              clinicalImportance:
                  'PAMP is the gold standard for PH diagnosis by catheterization. PAMP >20 mmHg defines pulmonary hypertension. More accurate than PASP for hemodynamic assessment.',
            ),
            _buildModernParameterCard(
              'Pulmonary Artery Diastolic Pressure (PADP)',
              'Normal: 4-12 mmHg • Elevated: >15 mmHg',
              'Calculated from PR end-diastolic velocity (4 × PR EDV² + RA pressure)',
              normalRange: [4, 12],
              currentValue: 8,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [12, 18],
                'moderate': [19, 25],
                'severe': [26, 40],
              },
              clinicalImportance:
                  'PADP reflects pulmonary vascular resistance and compliance. Elevated PADP indicates increased afterload and vascular remodeling. Important for complete pressure profile.',
            ),
            _buildModernParameterCard(
              'Transpulmonary Gradient (TPG)',
              'Normal: <12 mmHg • Elevated: >12 mmHg',
              'Calculated as PAMP - PCWP (pulmonary capillary wedge pressure)',
              normalRange: [5, 12],
              currentValue: 8,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [12, 20],
                'moderate': [21, 30],
                'severe': [31, 50],
              },
              clinicalImportance:
                  'TPG distinguishes pre-capillary from post-capillary PH. TPG >12 mmHg indicates pre-capillary PH. Critical for PH classification and treatment decisions.',
            ),
            _buildModernParameterCard(
              'Pulmonary Arterial Compliance',
              'Normal: >2.0 mL/mmHg • Reduced: <1.5 mL/mmHg',
              'Calculated as Stroke Volume / (PASP - PADP)',
              normalRange: [2.0, 4.0],
              currentValue: 2.8,
              unit: 'mL/mmHg',
              abnormalRanges: {
                'mild': [1.5, 1.9],
                'moderate': [1.0, 1.4],
                'severe': [0.5, 0.9],
              },
              clinicalImportance:
                  'PA compliance reflects arterial stiffness and wave reflections. Reduced compliance indicates advanced pulmonary vascular disease and poor prognosis in PH.',
            ),
          ]),

          _buildModernSectionHeader('RV Energetics', FontAwesomeIcons.bolt, [
            Color(0xFFE91E63),
            Color(0xFFF06292),
          ], 'rvEnergetics'),
          _buildCollapsibleSection('rvEnergetics', [
            _buildModernParameterCard(
              'RV Stroke Work',
              'Normal: 5-15 g·m • Elevated: >20 g·m',
              'Work performed by RV per beat (SV × PASP × 0.0136)',
              normalRange: [5, 15],
              currentValue: 10,
              unit: 'g·m',
              abnormalRanges: {
                'mild': [15, 25],
                'moderate': [26, 35],
                'severe': [36, 60],
              },
              clinicalImportance:
                  'RV stroke work quantifies mechanical work per beat. Elevated values indicate increased RV workload and energy expenditure. Important for assessing RV efficiency.',
            ),
            _buildModernParameterCard(
              'RV Power Output',
              'Normal: 0.5-2.0 W • Elevated: >3.0 W',
              'Rate of RV work performance (Stroke Work × Heart Rate)',
              normalRange: [0.5, 2.0],
              currentValue: 1.2,
              unit: 'W',
              abnormalRanges: {
                'mild': [2.0, 3.0],
                'moderate': [3.1, 4.5],
                'severe': [4.6, 8.0],
              },
              clinicalImportance:
                  'RV power output reflects total mechanical power generated by RV. Elevated values indicate high energy demands and potential for RV fatigue.',
            ),
            _buildModernParameterCard(
              'RV Efficiency',
              'Normal: >60% • Reduced: <40%',
              'Ratio of useful work to total energy expenditure',
              normalRange: [60, 80],
              currentValue: 70,
              unit: '%',
              abnormalRanges: {
                'mild': [40, 59],
                'moderate': [25, 39],
                'severe': [10, 24],
              },
              clinicalImportance:
                  'RV efficiency reflects mechanical efficiency of energy utilization. Reduced efficiency indicates wasted energy and poor RV performance. Critical for understanding RV adaptation.',
            ),
            _buildModernParameterCard(
              'RV Mechanical Work Index',
              'Normal: 3-8 g·m/m² • Elevated: >12 g·m/m²',
              'BSA-indexed RV stroke work for body size normalization',
              normalRange: [3, 8],
              currentValue: 5.5,
              unit: 'g·m/m²',
              abnormalRanges: {
                'mild': [8, 12],
                'moderate': [13, 18],
                'severe': [19, 30],
              },
              clinicalImportance:
                  'RV work index normalizes work for body size. Elevated values indicate excessive RV workload relative to body size. Important for comparing across different patients.',
            ),
          ]),

          _buildModernSectionHeader(
            'Exercise/Stress RV Parameters',
            FontAwesomeIcons.dumbbell,
            [Color(0xFF4CAF50), Color(0xFF81C784)],
            'rvExercise',
          ),
          _buildCollapsibleSection('rvExercise', [
            _buildModernParameterCard(
              'RV Reserve Function',
              'Normal: >50% increase • Reduced: <25% increase',
              'RV response to exercise (change in TAPSE or strain)',
              normalRange: [50, 100],
              currentValue: 70,
              unit: '%',
              abnormalRanges: {
                'mild': [25, 49],
                'moderate': [10, 24],
                'severe': [0, 9],
              },
              clinicalImportance:
                  'RV reserve function reflects RV contractile reserve and adaptation to increased demands. Reduced reserve indicates limited RV capacity and poor prognosis.',
            ),
            _buildModernParameterCard(
              'Exercise PASP',
              'Normal: <40 mmHg • Abnormal: >50 mmHg',
              'Stress-induced pulmonary pressure changes',
              normalRange: [25, 40],
              currentValue: 35,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [40, 50],
                'moderate': [51, 65],
                'severe': [66, 100],
              },
              clinicalImportance:
                  'Exercise PASP reveals latent pulmonary hypertension and RV dysfunction. Excessive rise indicates poor pulmonary vascular reserve and early disease.',
            ),
            _buildModernParameterCard(
              'RV Contractile Reserve',
              'Normal: >20% increase • Reduced: <10% increase',
              'TAPSE or strain response to stress (dobutamine/exercise)',
              normalRange: [20, 60],
              currentValue: 35,
              unit: '%',
              abnormalRanges: {
                'mild': [10, 19],
                'moderate': [5, 9],
                'severe': [0, 4],
              },
              clinicalImportance:
                  'RV contractile reserve assesses intrinsic RV contractile capacity. Reduced reserve indicates RV dysfunction and predicts poor outcomes in heart failure.',
            ),
          ]),

          _buildModernSectionHeader(
            'RV Outflow Tract Assessment',
            FontAwesomeIcons.arrowRight,
            [Color(0xFF9E9E9E), Color(0xFFBDBDBD)],
            'rvOutflow',
          ),
          _buildCollapsibleSection('rvOutflow', [
            _buildModernParameterCard(
              'RVOT Proximal Diameter',
              'Normal: 2.5-3.3 cm • Mild: 3.3-4.0 cm',
              'Measured at subpulmonary level in PSAX view',
              normalRange: [2.5, 3.3],
              currentValue: 2.9,
              unit: 'cm',
              abnormalRanges: {
                'mild': [3.3, 4.0],
                'moderate': [4.1, 5.0],
                'severe': [5.1, 7.0],
              },
              clinicalImportance:
                  'RVOT proximal diameter reflects RV outflow geometry and remodeling. Enlarged RVOT indicates RV pressure/volume overload and outflow tract dilatation.',
            ),
            _buildModernParameterCard(
              'RVOT Mid Diameter',
              'Normal: 2.0-2.7 cm • Mild: 2.7-3.5 cm',
              'Measured at mid-RVOT level',
              normalRange: [2.0, 2.7],
              currentValue: 2.4,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.7, 3.5],
                'moderate': [3.6, 4.5],
                'severe': [4.6, 6.0],
              },
              clinicalImportance:
                  'RVOT mid diameter provides assessment of outflow tract geometry. Important for understanding RVOT remodeling patterns and flow dynamics.',
            ),
            _buildModernParameterCard(
              'RVOT Distal Diameter',
              'Normal: 1.8-2.5 cm • Mild: 2.5-3.2 cm',
              'Measured at pulmonary valve level',
              normalRange: [1.8, 2.5],
              currentValue: 2.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.5, 3.2],
                'moderate': [3.3, 4.0],
                'severe': [4.1, 5.5],
              },
              clinicalImportance:
                  'RVOT distal diameter at PV level reflects valve annulus size and outflow geometry. Important for valve interventions and flow assessment.',
            ),
            _buildModernParameterCard(
              'RVOT Acceleration Time',
              'Normal: >105 ms • Mild PH: 80-105 ms',
              'Alternative to PAT for pulmonary pressure assessment',
              normalRange: [105, 150],
              currentValue: 125,
              unit: 'ms',
              abnormalRanges: {
                'mild': [80, 104],
                'moderate': [60, 79],
                'severe': [30, 59],
              },
              clinicalImportance:
                  'RVOT acceleration time reflects pulmonary vascular resistance. Shortened AT indicates elevated pulmonary pressures and increased afterload.',
            ),
            _buildModernParameterCard(
              'RVOT Ejection Pattern Analysis',
              'Normal: Smooth dome • Abnormal: Notched/bifid',
              'Shape analysis for PH assessment and flow dynamics',
              normalRange: [1, 1],
              currentValue: 1,
              unit: 'Pattern',
              abnormalRanges: {
                'mild': [2, 2],
                'moderate': [3, 3],
                'severe': [4, 4],
              },
              clinicalImportance:
                  'RVOT ejection pattern reflects flow dynamics and pulmonary vascular properties. Notched or bifid patterns indicate elevated pulmonary pressures and wave reflections.',
            ),
          ]),

          _buildModernSectionHeader(
            'Tricuspid Valve Quantitative Assessment',
            FontAwesomeIcons.heartCircleBolt,
            [Color(0xFF673AB7), Color(0xFF9575CD)],
            'rvTricuspidQuant',
          ),
          _buildCollapsibleSection('rvTricuspidQuant', [
            _buildModernParameterCard(
              'TR Effective Regurgitant Orifice Area (EROA)',
              'Mild: <0.20 cm² • Moderate: 0.20-0.39 cm²',
              'Calculated from PISA method (2πr² × Valiasing / Vmax)',
              normalRange: [0.0, 0.20],
              currentValue: 0.15,
              unit: 'cm²',
              abnormalRanges: {
                'mild': [0.20, 0.39],
                'moderate': [0.40, 0.59],
                'severe': [0.60, 1.50],
              },
              clinicalImportance:
                  'TR EROA is the most accurate method for TR quantification. EROA ≥0.40 cm² indicates severe TR. Critical for surgical decision-making and prognosis.',
            ),
            _buildModernParameterCard(
              'TR Regurgitant Volume',
              'Mild: <30 mL • Moderate: 30-44 mL',
              'Volume of blood regurgitating per beat (EROA × VTI)',
              normalRange: [0, 30],
              currentValue: 20,
              unit: 'mL',
              abnormalRanges: {
                'mild': [30, 44],
                'moderate': [45, 59],
                'severe': [60, 120],
              },
              clinicalImportance:
                  'TR regurgitant volume quantifies the amount of regurgitation. Volume ≥45 mL indicates severe TR. Important for assessing hemodynamic impact.',
            ),
            _buildModernParameterCard(
              'TR Regurgitant Fraction',
              'Mild: <30% • Moderate: 30-49%',
              'Percentage of regurgitant flow (Regurgitant Volume / Total SV)',
              normalRange: [0, 30],
              currentValue: 20,
              unit: '%',
              abnormalRanges: {
                'mild': [30, 49],
                'moderate': [50, 59],
                'severe': [60, 85],
              },
              clinicalImportance:
                  'TR regurgitant fraction reflects the proportion of ineffective flow. Fraction ≥50% indicates severe TR. Important for assessing RV volume overload.',
            ),
            _buildModernParameterCard(
              'TR Vena Contracta Width',
              'Mild: <0.3 cm • Moderate: 0.3-0.6 cm',
              'Narrowest portion of regurgitant jet',
              normalRange: [0.0, 0.3],
              currentValue: 0.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.3, 0.6],
                'moderate': [0.7, 0.9],
                'severe': [1.0, 2.0],
              },
              clinicalImportance:
                  'TR vena contracta is independent of loading conditions. Width >0.7 cm indicates severe TR. Simple and reliable method for TR assessment.',
            ),
          ]),

          _buildModernSectionHeader(
            'RV Wall Motion Assessment',
            FontAwesomeIcons.heartPulse,
            [Color(0xFFFF5722), Color(0xFFFF8A65)],
            'rvWallMotion',
          ),
          _buildCollapsibleSection('rvWallMotion', [
            _buildModernParameterCard(
              'RV Regional Wall Motion',
              'Normal: All segments normokinetic',
              'Segmental analysis of RV free wall and septum',
              normalRange: [1, 1],
              currentValue: 1,
              unit: 'Grade',
              abnormalRanges: {
                'mild': [2, 2],
                'moderate': [3, 3],
                'severe': [4, 4],
              },
              clinicalImportance:
                  'RV regional wall motion reflects segmental function and ischemia. Abnormal motion indicates RV dysfunction, ischemia, or structural disease.',
            ),
            _buildModernParameterCard(
              'RV Wall Motion Score Index',
              'Normal: 1.0 • Mild: 1.1-1.5',
              'Quantitative regional function (sum of scores / number of segments)',
              normalRange: [1.0, 1.0],
              currentValue: 1.0,
              unit: '',
              abnormalRanges: {
                'mild': [1.1, 1.5],
                'moderate': [1.6, 2.0],
                'severe': [2.1, 3.0],
              },
              clinicalImportance:
                  'RV WMSI quantifies overall regional function. Elevated scores indicate widespread RV dysfunction. Important for risk stratification and prognosis.',
            ),
            _buildModernParameterCard(
              'RV Apical vs Basal Function',
              'Normal: Apical > Basal • Abnormal: Apical < Basal',
              'Regional dysfunction pattern analysis',
              normalRange: [1.1, 2.0],
              currentValue: 1.3,
              unit: 'Ratio',
              abnormalRanges: {
                'mild': [0.8, 1.0],
                'moderate': [0.6, 0.7],
                'severe': [0.3, 0.5],
              },
              clinicalImportance:
                  'RV apical-basal function gradient reflects RV remodeling patterns. Reversed gradient indicates advanced RV dysfunction and poor prognosis.',
            ),
            _buildModernParameterCard(
              'RV Free Wall vs Septal Function',
              'Normal: Free wall > Septal • Abnormal: Free wall < Septal',
              'Comparison of RV free wall and septal contribution',
              normalRange: [1.2, 2.0],
              currentValue: 1.5,
              unit: 'Ratio',
              abnormalRanges: {
                'mild': [0.8, 1.1],
                'moderate': [0.6, 0.7],
                'severe': [0.3, 0.5],
              },
              clinicalImportance:
                  'RV free wall-septal function ratio reflects RV remodeling and interventricular dependence. Abnormal ratios indicate RV dysfunction and septal shift.',
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildRightAtriumContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernSectionHeader(
            'Right Atrial Dimensions',
            FontAwesomeIcons.ruler,
            [Color(0xFF2ECC71), Color(0xFF58D68D)],
            'raDimensions',
          ),
          _buildCollapsibleSection('raDimensions', [
            _buildModernParameterCard(
              'RA Area (4-Chamber View)',
              'Normal: <18 cm² • Mild: 18-25 cm²',
              'Measured in apical 4-chamber view at end-systole',
              normalRange: [10, 18],
              currentValue: 14,
              unit: 'cm²',
              abnormalRanges: {
                'mild': [18, 25],
                'moderate': [26, 35],
                'severe': [36, 60],
              },
              clinicalImportance:
                  'RA area reflects RA size and pressure overload. Enlarged RA indicates tricuspid regurgitation, pulmonary hypertension, or RV dysfunction. Important for assessing right heart remodeling.',
            ),
            _buildModernParameterCard(
              'RA Volume (Biplane Method)',
              'Normal: <33 mL/m² • Mild: 33-39 mL/m²',
              'BSA indexed volume using biplane area-length method',
              normalRange: [18, 33],
              currentValue: 25,
              unit: 'mL/m²',
              abnormalRanges: {
                'mild': [33, 39],
                'moderate': [40, 50],
                'severe': [51, 80],
              },
              clinicalImportance:
                  'RA volume is more accurate than area for assessing RA size. Indexed RA volume >33 mL/m² indicates RA enlargement. Strong predictor of right heart dysfunction and outcomes.',
            ),
            _buildModernParameterCard(
              'RA Major Axis Length',
              'Normal: <5.3 cm • Mild: 5.3-6.0 cm',
              'Measured from superior to inferior RA border',
              normalRange: [3.5, 5.3],
              currentValue: 4.5,
              unit: 'cm',
              abnormalRanges: {
                'mild': [5.3, 6.0],
                'moderate': [6.1, 7.0],
                'severe': [7.1, 9.0],
              },
              clinicalImportance:
                  'RA major axis length provides additional assessment of RA size. Useful for detecting RA enlargement and monitoring disease progression.',
            ),
            _buildModernParameterCard(
              'RA Minor Axis Length',
              'Normal: <4.4 cm • Mild: 4.4-5.0 cm',
              'Measured perpendicular to major axis',
              normalRange: [2.5, 4.4],
              currentValue: 3.5,
              unit: 'cm',
              abnormalRanges: {
                'mild': [4.4, 5.0],
                'moderate': [5.1, 6.0],
                'severe': [6.1, 8.0],
              },
              clinicalImportance:
                  'RA minor axis length complements major axis measurement for comprehensive RA size assessment. Important for detecting RA remodeling patterns.',
            ),
          ]),

          _buildModernSectionHeader(
            'Right Atrial Function',
            FontAwesomeIcons.heartCircleCheck,
            [Color(0xFF9B59B6), Color(0xFFBB7FD1)],
            'raFunction',
          ),
          _buildCollapsibleSection('raFunction', [
            _buildModernParameterCard(
              'RA Emptying Fraction (Total)',
              'Normal: >40% • Borderline: 30-40%',
              'Calculated as (RAVmax - RAVmin) / RAVmax × 100',
              normalRange: [40, 65],
              currentValue: 50,
              unit: '%',
              abnormalRanges: {
                'mild': [30, 39],
                'moderate': [20, 29],
                'severe': [0, 19],
              },
              clinicalImportance:
                  'RA emptying fraction reflects overall RA function. Reduced values indicate RA dysfunction and are associated with poor outcomes in right heart disease.',
            ),
            _buildModernParameterCard(
              'RA Passive Emptying Fraction',
              'Normal: 25-35% • Borderline: 15-25%',
              'Early diastolic emptying during RV filling',
              normalRange: [25, 35],
              currentValue: 30,
              unit: '%',
              abnormalRanges: {
                'mild': [15, 24],
                'moderate': [10, 14],
                'severe': [0, 9],
              },
              clinicalImportance:
                  'RA passive emptying reflects RA conduit function during early diastole. Reduced values indicate impaired RV relaxation and elevated filling pressures.',
            ),
            _buildModernParameterCard(
              'RA Active Emptying Fraction',
              'Normal: 20-30% • Borderline: 10-20%',
              'Late diastolic emptying during atrial contraction',
              normalRange: [20, 30],
              currentValue: 25,
              unit: '%',
              abnormalRanges: {
                'mild': [10, 19],
                'moderate': [5, 9],
                'severe': [0, 4],
              },
              clinicalImportance:
                  'RA active emptying reflects RA contractile function (booster pump). Reduced values indicate intrinsic RA dysfunction. Lost in atrial fibrillation.',
            ),
          ]),

          _buildModernSectionHeader(
            'Right Atrial Pressure Assessment',
            FontAwesomeIcons.waveSquare,
            [Color(0xFF4ECDC4), Color(0xFF6EE7E0)],
            'raPressure',
          ),
          _buildCollapsibleSection('raPressure', [
            _buildModernParameterCard(
              'RA Pressure (IVC Method)',
              'Normal: 3-8 mmHg • Elevated: >15 mmHg',
              'Estimated from IVC diameter and respiratory variation',
              normalRange: [3, 8],
              currentValue: 5,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [9, 15],
                'moderate': [16, 20],
                'severe': [21, 35],
              },
              clinicalImportance:
                  'RA pressure reflects right heart filling pressures and volume status. Elevated RA pressure indicates right heart failure, tricuspid regurgitation, or volume overload.',
            ),
            _buildModernParameterCard(
              'IVC Diameter',
              'Normal: <2.1 cm • Mild: 2.1-2.5 cm',
              'Measured 2 cm from RA junction during expiration',
              normalRange: [1.2, 2.1],
              currentValue: 1.8,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.1, 2.5],
                'moderate': [2.6, 3.0],
                'severe': [3.1, 4.0],
              },
              clinicalImportance:
                  'IVC diameter reflects RA pressure and volume status. Dilated IVC indicates elevated RA pressure and right heart dysfunction. Essential for hemodynamic assessment.',
            ),
            _buildModernParameterCard(
              'IVC Respiratory Variation',
              'Normal: >50% • Reduced: 35-50%',
              'Percentage decrease in IVC diameter with inspiration',
              normalRange: [50, 80],
              currentValue: 60,
              unit: '%',
              abnormalRanges: {
                'mild': [35, 49],
                'moderate': [20, 34],
                'severe': [0, 19],
              },
              clinicalImportance:
                  'IVC respiratory variation reflects RA pressure. Reduced variation indicates elevated RA pressure and impaired venous return. Critical for assessing volume status.',
            ),
          ]),

          _buildModernSectionHeader(
            'Advanced RA Parameters',
            FontAwesomeIcons.chartLine,
            [Color(0xFFFF9500), Color(0xFFFFB347)],
            'raAdvanced',
          ),
          _buildCollapsibleSection('raAdvanced', [
            _buildModernParameterCard(
              'RA Reservoir Strain',
              'Normal: 39-46% • Borderline: 25-38%',
              'Peak positive strain during RV systole (Meta-analysis: 42.7%)',
              normalRange: [39, 46],
              currentValue: 42,
              unit: '%',
              abnormalRanges: {
                'mild': [25, 38],
                'moderate': [15, 24],
                'severe': [0, 14],
              },
              clinicalImportance:
                  'RA reservoir strain reflects RA compliance and filling capacity. Normal value 42.7% (95% CI: 39.4-45.9%) from latest meta-analysis. Reduced values indicate RA fibrosis and dysfunction. Early marker of RA remodeling before size changes.',
            ),
            _buildModernParameterCard(
              'RA Conduit Strain',
              'Normal: 21-27% • Borderline: 15-20%',
              'Strain during early RV diastole (Meta-analysis: 23.6%)',
              normalRange: [21, 27],
              currentValue: 24,
              unit: '%',
              abnormalRanges: {
                'mild': [15, 20],
                'moderate': [10, 14],
                'severe': [0, 9],
              },
              clinicalImportance:
                  'RA conduit strain reflects passive RA emptying and RV relaxation. Normal value 23.6% (95% CI: 20.7-26.6%) from latest research. Reduced values indicate impaired RV relaxation and elevated filling pressures.',
            ),
            _buildModernParameterCard(
              'RA Contractile Strain',
              'Normal: 14-19% • Borderline: 10-13%',
              'Strain during late RV diastole (Meta-analysis: 16.1%)',
              normalRange: [14, 19],
              currentValue: 16,
              unit: '%',
              abnormalRanges: {
                'mild': [10, 13],
                'moderate': [5, 9],
                'severe': [0, 4],
              },
              clinicalImportance:
                  'RA contractile strain reflects intrinsic RA contractile function. Normal value 16.1% (95% CI: 13.6-18.6%) from latest meta-analysis. Reduced values indicate RA myopathy and dysfunction. Lost in atrial fibrillation.',
            ),
            _buildModernParameterCard(
              'RA Stiffness Index',
              'Normal: <0.6 • Borderline: 0.6-1.0',
              'Calculated as RA pressure / RA reservoir strain',
              normalRange: [0.1, 0.6],
              currentValue: 0.4,
              unit: '',
              abnormalRanges: {
                'mild': [0.6, 1.0],
                'moderate': [1.1, 1.5],
                'severe': [1.6, 3.0],
              },
              clinicalImportance:
                  'RA stiffness index combines filling pressures and RA function. Elevated values indicate increased RA stiffness and poor compliance. Predictor of right heart outcomes.',
            ),
          ]),

          _buildModernSectionHeader(
            'RA Strain Rate Parameters',
            FontAwesomeIcons.gaugeHigh,
            [Color(0xFF1ABC9C), Color(0xFF48C9B0)],
            'raStrainRate',
          ),
          _buildCollapsibleSection('raStrainRate', [
            _buildModernParameterCard(
              'RA Reservoir Strain Rate (SRs)',
              'Normal: 2.0-2.1 s⁻¹ • Borderline: 1.5-1.9 s⁻¹',
              'Peak positive strain rate during RV systole (Meta-analysis: 2.1 s⁻¹)',
              normalRange: [2.0, 2.1],
              currentValue: 2.05,
              unit: 's⁻¹',
              abnormalRanges: {
                'mild': [1.5, 1.9],
                'moderate': [1.0, 1.4],
                'severe': [0.5, 0.9],
              },
              clinicalImportance:
                  'RA reservoir strain rate reflects RA filling rate and compliance. Normal value 2.1 s⁻¹ (95% CI: 2.0-2.1) from meta-analysis. More sensitive than strain for detecting early RA dysfunction and predicting atrial fibrillation.',
            ),
            _buildModernParameterCard(
              'RA Conduit Strain Rate (SRe)',
              'Normal: -1.7 to -2.2 s⁻¹ • Borderline: -1.0 to -1.6 s⁻¹',
              'Peak negative strain rate during early RV diastole (Meta-analysis: -1.9 s⁻¹)',
              normalRange: [-2.2, -1.7],
              currentValue: -1.9,
              unit: 's⁻¹',
              abnormalRanges: {
                'mild': [-1.6, -1.0],
                'moderate': [-0.9, -0.5],
                'severe': [-0.4, 0.0],
              },
              clinicalImportance:
                  'RA conduit strain rate reflects passive RA emptying rate and RV relaxation. Normal value -1.9 s⁻¹ (95% CI: -2.2 to -1.7). Reduced magnitude indicates impaired RV relaxation and elevated filling pressures.',
            ),
            _buildModernParameterCard(
              'RA Contractile Strain Rate (SRa)',
              'Normal: -1.5 to -2.0 s⁻¹ • Borderline: -1.0 to -1.4 s⁻¹',
              'Peak negative strain rate during late RV diastole (Meta-analysis: -1.8 s⁻¹)',
              normalRange: [-2.0, -1.5],
              currentValue: -1.8,
              unit: 's⁻¹',
              abnormalRanges: {
                'mild': [-1.4, -1.0],
                'moderate': [-0.9, -0.5],
                'severe': [-0.4, 0.0],
              },
              clinicalImportance:
                  'RA contractile strain rate reflects RA contractile velocity and intrinsic function. Normal value -1.8 s⁻¹ (95% CI: -2.0 to -1.5). Reduced magnitude indicates RA myopathy. Lost in atrial fibrillation.',
            ),
            _buildModernParameterCard(
              'RA Strain Rate Ratio (SRs/SRa)',
              'Normal: 1.0-1.4 • Borderline: 0.7-0.9',
              'Ratio of reservoir to contractile strain rates',
              normalRange: [1.0, 1.4],
              currentValue: 1.1,
              unit: '',
              abnormalRanges: {
                'mild': [0.7, 0.9],
                'moderate': [0.5, 0.6],
                'severe': [0.2, 0.4],
              },
              clinicalImportance:
                  'RA strain rate ratio reflects balance between passive and active RA function. Altered ratio indicates RA remodeling and dysfunction. Useful for characterizing RA functional phenotypes in different diseases.',
            ),
          ]),

          _buildModernSectionHeader(
            'RA Synchrony Assessment',
            FontAwesomeIcons.clockRotateLeft,
            [Color(0xFFE67E22), Color(0xFFF39C12)],
            'raSynchrony',
          ),
          _buildCollapsibleSection('raSynchrony', [
            _buildModernParameterCard(
              'Intra-RA Dyssynchrony Index',
              'Normal: <50 ms • Borderline: 50-70 ms',
              'Standard deviation of time to peak strain across RA segments',
              normalRange: [20, 50],
              currentValue: 35,
              unit: 'ms',
              abnormalRanges: {
                'mild': [50, 70],
                'moderate': [70, 90],
                'severe': [90, 150],
              },
              clinicalImportance:
                  'Intra-RA dyssynchrony reflects regional RA activation heterogeneity. Increased dyssynchrony predicts atrial fibrillation development and poor outcomes. Early marker of RA electrical remodeling and conduction abnormalities.',
            ),
            _buildModernParameterCard(
              'RA-RV Coupling Index',
              'Normal: 0.8-1.2 • Borderline: 0.6-0.79',
              'Temporal relationship between RA and RV strain peaks',
              normalRange: [0.8, 1.2],
              currentValue: 1.0,
              unit: '',
              abnormalRanges: {
                'mild': [0.6, 0.79],
                'moderate': [0.4, 0.59],
                'severe': [0.2, 0.39],
              },
              clinicalImportance:
                  'RA-RV coupling index reflects temporal coordination between RA and RV function. Altered coupling indicates right heart dyssynchrony and impaired hemodynamic efficiency. Important for understanding right heart failure mechanisms.',
            ),
            _buildModernParameterCard(
              'RA Electrical Activation Time',
              'Normal: 80-120 ms • Prolonged: >150 ms',
              'Time from P-wave onset to peak RA strain',
              normalRange: [80, 120],
              currentValue: 100,
              unit: 'ms',
              abnormalRanges: {
                'mild': [120, 150],
                'moderate': [150, 200],
                'severe': [200, 300],
              },
              clinicalImportance:
                  'RA electrical activation time reflects RA conduction velocity and electrical function. Prolonged activation indicates RA conduction abnormalities and increased arrhythmia risk. Useful for assessing RA electrical remodeling.',
            ),
            _buildModernParameterCard(
              'RA Synchrony Index',
              'Normal: >85% • Borderline: 70-85%',
              'Percentage of RA segments with synchronized contraction',
              normalRange: [85, 100],
              currentValue: 92,
              unit: '%',
              abnormalRanges: {
                'mild': [70, 84],
                'moderate': [50, 69],
                'severe': [20, 49],
              },
              clinicalImportance:
                  'RA synchrony index quantifies coordinated RA contraction. Reduced synchrony indicates RA dyssynchrony and electrical heterogeneity. Strong predictor of atrial fibrillation and right heart dysfunction.',
            ),
            _buildModernParameterCard(
              'RA Conduction Velocity',
              'Normal: 0.4-0.8 m/s • Slow: <0.4 m/s',
              'Speed of electrical activation across RA myocardium',
              normalRange: [0.4, 0.8],
              currentValue: 0.6,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [0.3, 0.39],
                'moderate': [0.2, 0.29],
                'severe': [0.1, 0.19],
              },
              clinicalImportance:
                  'RA conduction velocity reflects RA electrical properties and myocardial integrity. Slow conduction indicates RA fibrosis and electrical remodeling. Associated with increased arrhythmia susceptibility and poor outcomes.',
            ),
          ]),

          _buildModernSectionHeader(
            'Tricuspid Valve Assessment',
            FontAwesomeIcons.heartCircleBolt,
            [Color(0xFF8E44AD), Color(0xFFAB7AC7)],
            'raTricuspid',
          ),
          _buildCollapsibleSection('raTricuspid', [
            _buildModernParameterCard(
              'Tricuspid Annulus Diameter',
              'Normal: <4.0 cm • Mild: 4.0-4.7 cm',
              'Measured in apical 4-chamber view at end-systole',
              normalRange: [2.8, 4.0],
              currentValue: 3.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [4.0, 4.7],
                'moderate': [4.8, 5.5],
                'severe': [5.6, 7.0],
              },
              clinicalImportance:
                  'Tricuspid annulus diameter reflects tricuspid valve size and function. Enlarged annulus indicates tricuspid regurgitation and right heart remodeling.',
            ),
            _buildModernParameterCard(
              'Tricuspid Regurgitation Velocity',
              'Normal: <2.8 m/s • Mild PH: 2.8-3.4 m/s',
              'Peak velocity of tricuspid regurgitation jet',
              normalRange: [2.0, 2.8],
              currentValue: 2.4,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [2.8, 3.4],
                'moderate': [3.5, 4.0],
                'severe': [4.1, 6.0],
              },
              clinicalImportance:
                  'TR velocity estimates RV systolic pressure and pulmonary artery pressure. Elevated TR velocity indicates pulmonary hypertension. Most important parameter for estimating PASP.',
            ),
            _buildModernParameterCard(
              'Tricuspid Valve Tenting Area',
              'Normal: <1.6 cm² • Mild: 1.6-2.5 cm²',
              'Area between tricuspid leaflets and annular plane',
              normalRange: [0.5, 1.6],
              currentValue: 1.0,
              unit: 'cm²',
              abnormalRanges: {
                'mild': [1.6, 2.5],
                'moderate': [2.6, 3.5],
                'severe': [3.6, 6.0],
              },
              clinicalImportance:
                  'Tricuspid tenting area reflects tricuspid valve deformation. Increased tenting indicates functional tricuspid regurgitation due to RV remodeling.',
            ),
          ]),

          _buildModernSectionHeader(
            'Hepatic Vein Flow Assessment',
            FontAwesomeIcons.droplet,
            [Color(0xFF27AE60), Color(0xFF58D68D)],
            'raHepatic',
          ),
          _buildCollapsibleSection('raHepatic', [
            _buildModernParameterCard(
              'Hepatic Vein S/D Ratio',
              'Normal: >1.0 • Abnormal: <0.5',
              'Ratio of systolic to diastolic forward flow',
              normalRange: [1.0, 2.0],
              currentValue: 1.4,
              unit: '',
              abnormalRanges: {
                'mild': [0.5, 0.9],
                'moderate': [0.3, 0.4],
                'severe': [0.0, 0.2],
              },
              clinicalImportance:
                  'Hepatic vein S/D ratio reflects RA pressure and tricuspid regurgitation severity. Reduced ratio indicates elevated RA pressure and significant TR.',
            ),
            _buildModernParameterCard(
              'Hepatic Vein Atrial Reversal',
              'Normal: <35 cm/s • Mild: 35-45 cm/s',
              'Peak velocity of atrial reversal flow',
              normalRange: [10, 35],
              currentValue: 25,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [35, 45],
                'moderate': [46, 60],
                'severe': [61, 100],
              },
              clinicalImportance:
                  'Hepatic vein atrial reversal reflects RA pressure and compliance. Increased reversal indicates elevated RA pressure and impaired RA function.',
            ),
          ]),

          _buildModernSectionHeader(
            'Superior Vena Cava Flow Assessment',
            FontAwesomeIcons.arrowUp,
            [Color(0xFF3498DB), Color(0xFF5DADE2)],
            'raSVC',
          ),
          _buildCollapsibleSection('raSVC', [
            _buildModernParameterCard(
              'SVC S/D Ratio',
              'Normal: >1.0 • Abnormal: <0.5',
              'Ratio of systolic to diastolic forward flow in SVC',
              normalRange: [1.0, 2.0],
              currentValue: 1.3,
              unit: '',
              abnormalRanges: {
                'mild': [0.5, 0.9],
                'moderate': [0.3, 0.4],
                'severe': [0.0, 0.2],
              },
              clinicalImportance:
                  'SVC S/D ratio reflects RA pressure and tricuspid regurgitation severity. Alternative to hepatic vein assessment when IVC is enlarged despite low RAP. Reduced ratio indicates elevated RA pressure.',
            ),
            _buildModernParameterCard(
              'SVC Peak Systolic Velocity',
              'Normal: 40-80 cm/s • Reduced: <40 cm/s',
              'Peak forward velocity during ventricular systole',
              normalRange: [40, 80],
              currentValue: 60,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [30, 39],
                'moderate': [20, 29],
                'severe': [10, 19],
              },
              clinicalImportance:
                  'SVC systolic velocity reflects venous return and RA function. Reduced velocity indicates impaired venous return or elevated RA pressure. Useful in upper body venous assessment.',
            ),
            _buildModernParameterCard(
              'SVC Peak Diastolic Velocity',
              'Normal: 30-60 cm/s • Reduced: <30 cm/s',
              'Peak forward velocity during ventricular diastole',
              normalRange: [30, 60],
              currentValue: 45,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [20, 29],
                'moderate': [15, 19],
                'severe': [5, 14],
              },
              clinicalImportance:
                  'SVC diastolic velocity reflects RV filling and compliance. Reduced velocity indicates impaired RV relaxation or elevated filling pressures. Important for diastolic function assessment.',
            ),
            _buildModernParameterCard(
              'SVC Respiratory Variation',
              'Normal: >25% • Reduced: <25%',
              'Percentage change in SVC flow with respiration',
              normalRange: [25, 50],
              currentValue: 35,
              unit: '%',
              abnormalRanges: {
                'mild': [15, 24],
                'moderate': [10, 14],
                'severe': [0, 9],
              },
              clinicalImportance:
                  'SVC respiratory variation reflects venous return dynamics and RA pressure. Reduced variation indicates elevated RA pressure or impaired venous compliance. Useful when IVC assessment is limited.',
            ),
            _buildModernParameterCard(
              'SVC Atrial Reversal Velocity',
              'Normal: <30 cm/s • Mild: 30-40 cm/s',
              'Peak velocity of atrial reversal flow in SVC',
              normalRange: [5, 30],
              currentValue: 20,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [30, 40],
                'moderate': [41, 55],
                'severe': [56, 80],
              },
              clinicalImportance:
                  'SVC atrial reversal reflects RA pressure and contractility. Increased reversal indicates elevated RA pressure and impaired RA compliance. Alternative assessment when hepatic veins are not accessible.',
            ),
          ]),

          _buildModernSectionHeader(
            'Tricuspid Inflow Assessment',
            FontAwesomeIcons.waveSquare,
            [Color(0xFFE67E22), Color(0xFFF39C12)],
            'raTricuspidInflow',
          ),
          _buildCollapsibleSection('raTricuspidInflow', [
            _buildModernParameterCard(
              'Tricuspid Inflow E/A Ratio',
              'Normal: 0.8-2.1 • Age-dependent parameter',
              'Ratio of early to late diastolic filling velocities',
              normalRange: [0.8, 2.1],
              currentValue: 1.4,
              unit: '',
              abnormalRanges: {
                'mild': [0.6, 0.79],
                'moderate': [2.2, 3.0],
                'severe': [0.0, 0.59],
              },
              clinicalImportance:
                  'Tricuspid E/A ratio reflects RV diastolic function and filling pattern. Reduced ratio indicates impaired RV relaxation. Elevated ratio may suggest restrictive filling or elevated RA pressure.',
            ),
            _buildModernParameterCard(
              'Tricuspid E Wave Velocity',
              'Normal: 36-71 cm/s • Age-dependent',
              'Peak early diastolic filling velocity',
              normalRange: [36, 71],
              currentValue: 55,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [25, 35],
                'moderate': [72, 90],
                'severe': [15, 24],
              },
              clinicalImportance:
                  'Tricuspid E velocity reflects RV filling and RA pressure. Reduced E velocity indicates impaired RV relaxation. Elevated E velocity may suggest elevated RA pressure or restrictive physiology.',
            ),
            _buildModernParameterCard(
              'Tricuspid A Wave Velocity',
              'Normal: 25-50 cm/s • Age-dependent',
              'Peak late diastolic filling velocity (atrial kick)',
              normalRange: [25, 50],
              currentValue: 38,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [51, 65],
                'moderate': [66, 80],
                'severe': [81, 120],
              },
              clinicalImportance:
                  'Tricuspid A velocity reflects RA contractile function and RV compliance. Elevated A velocity indicates compensatory RA contraction for impaired RV relaxation. Absent in atrial fibrillation.',
            ),
            _buildModernParameterCard(
              'Tricuspid Deceleration Time',
              'Normal: 120-200 ms • Age-dependent',
              'Time for E-wave velocity to decrease to baseline',
              normalRange: [120, 200],
              currentValue: 160,
              unit: 'ms',
              abnormalRanges: {
                'mild': [201, 250],
                'moderate': [80, 119],
                'severe': [50, 79],
              },
              clinicalImportance:
                  'Tricuspid DT reflects RV compliance and filling pressures. Prolonged DT indicates impaired RV relaxation. Shortened DT suggests elevated filling pressures and restrictive physiology.',
            ),
            _buildModernParameterCard(
              'Tricuspid Tissue Doppler e\' velocity',
              'Normal: >7 cm/s (lateral tricuspid annulus)',
              'Direct measure of RV myocardial relaxation',
              normalRange: [7, 15],
              currentValue: 10,
              unit: 'cm/s',
              abnormalRanges: {
                'mild': [5, 6],
                'moderate': [3, 4],
                'severe': [1, 2],
              },
              clinicalImportance:
                  'Tricuspid e\' velocity is the most reliable parameter for assessing RV diastolic function. Reduced e\' indicates impaired RV relaxation. Less affected by preload than tricuspid inflow parameters.',
            ),
            _buildModernParameterCard(
              'Tricuspid E/e\' Ratio',
              'Normal: <6 • Intermediate: 6-10 • Elevated: >10',
              'Best predictor of RV filling pressures',
              normalRange: [3, 6],
              currentValue: 5.5,
              unit: '',
              abnormalRanges: {
                'mild': [6, 8],
                'moderate': [8, 10],
                'severe': [10, 20],
              },
              clinicalImportance:
                  'Tricuspid E/e\' ratio is the best non-invasive predictor of RV filling pressures. Values >10 indicate elevated RVEDP. Critical for diagnosing right heart failure and pulmonary hypertension.',
            ),
          ]),

          _buildModernSectionHeader(
            'RA Mechanical Dispersion',
            FontAwesomeIcons.clockRotateLeft,
            [Color(0xFF8E44AD), Color(0xFFAB7AC7)],
            'raMechanical',
          ),
          _buildCollapsibleSection('raMechanical', [
            _buildModernParameterCard(
              'RA Mechanical Dispersion',
              'Normal: <45 ms • Borderline: 45-65 ms',
              'Standard deviation of time to peak strain across RA segments',
              normalRange: [20, 45],
              currentValue: 35,
              unit: 'ms',
              abnormalRanges: {
                'mild': [45, 65],
                'moderate': [65, 85],
                'severe': [85, 150],
              },
              clinicalImportance:
                  'RA mechanical dispersion reflects RA dyssynchrony and electrical heterogeneity. Increased dispersion predicts atrial fibrillation development and poor outcomes in right heart disease. Early marker of RA electrical remodeling.',
            ),
            _buildModernParameterCard(
              'Time to Peak RA Strain',
              'Normal: 300-400 ms • Uniform across segments',
              'Time from QRS onset to peak positive strain',
              normalRange: [300, 400],
              currentValue: 350,
              unit: 'ms',
              abnormalRanges: {
                'mild': [400, 450],
                'moderate': [450, 550],
                'severe': [550, 700],
              },
              clinicalImportance:
                  'Time to peak RA strain reflects RA activation timing and synchrony. Prolonged or variable timing indicates RA conduction abnormalities. Useful for assessing RA electrical function and arrhythmia risk.',
            ),
          ]),

          _buildModernSectionHeader(
            'RA Compliance Assessment',
            FontAwesomeIcons.scaleBalanced,
            [Color(0xFF16A085), Color(0xFF48C9B0)],
            'raCompliance',
          ),
          _buildCollapsibleSection('raCompliance', [
            _buildModernParameterCard(
              'RA Compliance',
              'Normal: >2.0 mL/mmHg • Borderline: 1.0-2.0 mL/mmHg',
              'Calculated as ΔRA volume / ΔRA pressure',
              normalRange: [2.0, 5.0],
              currentValue: 3.2,
              unit: 'mL/mmHg',
              abnormalRanges: {
                'mild': [1.0, 1.9],
                'moderate': [0.5, 0.9],
                'severe': [0.1, 0.4],
              },
              clinicalImportance:
                  'RA compliance reflects RA chamber stiffness and distensibility. Reduced compliance indicates RA fibrosis and right heart dysfunction. Important for understanding right heart failure mechanisms.',
            ),
            _buildModernParameterCard(
              'RA Pressure-Volume Relationship',
              'Normal: Linear relationship • Steep slope indicates stiffness',
              'Slope of pressure-volume curve during filling',
              normalRange: [0.1, 0.4],
              currentValue: 0.25,
              unit: 'mmHg/mL',
              abnormalRanges: {
                'mild': [0.4, 0.6],
                'moderate': [0.6, 1.0],
                'severe': [1.0, 2.5],
              },
              clinicalImportance:
                  'RA P-V relationship slope reflects RA stiffness and compliance. Steep slope indicates reduced compliance and elevated filling pressures. Important for understanding right heart diastolic dysfunction.',
            ),
          ]),

          _buildModernSectionHeader(
            'Enhanced Tricuspid Assessment',
            FontAwesomeIcons.heartCircleBolt,
            [Color(0xFFE74C3C), Color(0xFFEC7063)],
            'raEnhancedTricuspid',
          ),
          _buildCollapsibleSection('raEnhancedTricuspid', [
            _buildModernParameterCard(
              'Tricuspid Regurgitation Volume',
              'Normal: <30 mL • Mild: 30-44 mL',
              'Volume of blood regurgitating per beat',
              normalRange: [0, 30],
              currentValue: 15,
              unit: 'mL',
              abnormalRanges: {
                'mild': [30, 44],
                'moderate': [45, 59],
                'severe': [60, 120],
              },
              clinicalImportance:
                  'TR regurgitant volume quantifies TR severity. Values ≥45 mL indicate significant TR. Important for timing of tricuspid valve intervention and assessing hemodynamic impact on right heart.',
            ),
            _buildModernParameterCard(
              'Tricuspid Effective Regurgitant Orifice Area (EROA)',
              'Normal: <0.2 cm² • Mild: 0.2-0.39 cm²',
              'Effective area of tricuspid regurgitant orifice',
              normalRange: [0.0, 0.2],
              currentValue: 0.1,
              unit: 'cm²',
              abnormalRanges: {
                'mild': [0.2, 0.39],
                'moderate': [0.4, 0.59],
                'severe': [0.6, 1.5],
              },
              clinicalImportance:
                  'TR EROA is the most accurate parameter for TR quantification. Values ≥0.4 cm² indicate severe TR. Essential for surgical decision-making and risk stratification in tricuspid valve disease.',
            ),
            _buildModernParameterCard(
              'PISA Radius for TR',
              'Normal: <0.5 cm • Mild: 0.5-0.8 cm',
              'Radius of proximal isovelocity surface area for TR',
              normalRange: [0.0, 0.5],
              currentValue: 0.3,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.5, 0.8],
                'moderate': [0.8, 1.0],
                'severe': [1.0, 2.0],
              },
              clinicalImportance:
                  'PISA radius is used to calculate TR EROA and regurgitant volume. Values >0.9 cm indicate severe TR. Most accurate method for TR quantification when properly measured.',
            ),
            _buildModernParameterCard(
              'Tricuspid Valve Coaptation Depth',
              'Normal: <0.8 cm • Mild: 0.8-1.0 cm',
              'Distance from annular plane to leaflet coaptation point',
              normalRange: [0.2, 0.8],
              currentValue: 0.5,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.8, 1.0],
                'moderate': [1.1, 1.5],
                'severe': [1.6, 2.5],
              },
              clinicalImportance:
                  'Tricuspid coaptation depth reflects valve tethering and functional TR. Increased depth indicates RV remodeling and functional TR. Important for planning tricuspid valve repair strategies.',
            ),
            _buildModernParameterCard(
              'Tricuspid Valve Leaflet Separation',
              'Normal: <0.8 cm • Mild: 0.8-1.2 cm',
              'Distance between tricuspid leaflet tips at end-systole',
              normalRange: [0.2, 0.8],
              currentValue: 0.6,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.8, 1.2],
                'moderate': [1.3, 1.8],
                'severe': [1.9, 3.0],
              },
              clinicalImportance:
                  'Tricuspid leaflet separation indicates valve malcoaptation and functional TR severity. Increased separation reflects RV dilatation and annular dysfunction. Guides repair vs replacement decisions.',
            ),
            _buildModernParameterCard(
              'Tricuspid Annular Plane Systolic Excursion (TAPSE)',
              'Normal: >1.7 cm • Borderline: 1.4-1.7 cm',
              'Longitudinal motion of lateral tricuspid annulus',
              normalRange: [1.7, 2.8],
              currentValue: 2.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [1.4, 1.6],
                'moderate': [1.0, 1.3],
                'severe': [0.5, 0.9],
              },
              clinicalImportance:
                  'TAPSE reflects RV longitudinal systolic function. Reduced TAPSE indicates RV dysfunction and is associated with poor outcomes. Simple, reproducible parameter for RV function assessment.',
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildValvesContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernSectionHeader(
            'Mitral Valve Assessment',
            FontAwesomeIcons.heartCircleBolt,
            [Color(0xFF9B59B6), Color(0xFFBB7FD1)],
            'mitralValve',
          ),
          _buildCollapsibleSection('mitralValve', [
            _buildModernParameterCard(
              'Mitral Valve Area (MVA)',
              'Normal: 4-6 cm² • Mild MS: 1.5-2.5 cm²',
              'Measured by planimetry or pressure half-time method',
              normalRange: [4.0, 6.0],
              currentValue: 5.0,
              unit: 'cm²',
              abnormalRanges: {
                'mild': [1.5, 2.5],
                'moderate': [1.0, 1.4],
                'severe': [0.5, 0.9],
              },
              clinicalImportance:
                  'MVA is the gold standard for assessing mitral stenosis severity. Values <1.5 cm² indicate significant stenosis requiring intervention. Critical for timing of valve replacement or repair.',
            ),
            _buildModernParameterCard(
              'Mitral Regurgitation Vena Contracta',
              'Normal: <0.3 cm • Mild: 0.3-0.5 cm',
              'Narrowest portion of regurgitant jet',
              normalRange: [0.0, 0.3],
              currentValue: 0.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.3, 0.5],
                'moderate': [0.5, 0.7],
                'severe': [0.7, 1.5],
              },
              clinicalImportance:
                  'Vena contracta is the most reliable parameter for grading MR severity. Values ≥0.7 cm indicate severe MR requiring surgical intervention. Less affected by loading conditions than other parameters.',
            ),
            _buildModernParameterCard(
              'Mitral Regurgitation Volume',
              'Normal: <30 mL • Mild: 30-44 mL',
              'Volume of blood regurgitating per beat',
              normalRange: [0, 30],
              currentValue: 15,
              unit: 'mL',
              abnormalRanges: {
                'mild': [30, 44],
                'moderate': [45, 59],
                'severe': [60, 150],
              },
              clinicalImportance:
                  'Regurgitant volume quantifies MR severity. Values ≥60 mL indicate severe MR. Important for timing of surgical intervention and assessing hemodynamic impact.',
            ),
            _buildModernParameterCard(
              'Mitral Effective Regurgitant Orifice Area (EROA)',
              'Normal: <0.2 cm² • Mild: 0.2-0.29 cm²',
              'Effective area of regurgitant orifice',
              normalRange: [0.0, 0.2],
              currentValue: 0.1,
              unit: 'cm²',
              abnormalRanges: {
                'mild': [0.2, 0.29],
                'moderate': [0.3, 0.39],
                'severe': [0.4, 1.0],
              },
              clinicalImportance:
                  'EROA is the most accurate parameter for MR quantification. Values ≥0.4 cm² indicate severe MR. Essential for surgical decision-making and risk stratification.',
            ),
            _buildModernParameterCard(
              'Mitral Annulus Diameter',
              'Normal: 2.7-3.5 cm • Mild: 3.5-4.0 cm',
              'Measured in apical 4-chamber view',
              normalRange: [2.7, 3.5],
              currentValue: 3.1,
              unit: 'cm',
              abnormalRanges: {
                'mild': [3.5, 4.0],
                'moderate': [4.1, 4.8],
                'severe': [4.9, 6.0],
              },
              clinicalImportance:
                  'Mitral annulus diameter reflects annular dilatation. Enlarged annulus contributes to functional MR. Important for planning annuloplasty procedures.',
            ),
            _buildModernParameterCard(
              'Mitral Stenosis Pressure Half-Time',
              'Normal: N/A • Mild MS: 71-149 ms',
              'Time for pressure gradient to decrease by half',
              normalRange: [0, 70],
              currentValue: 50,
              unit: 'ms',
              abnormalRanges: {
                'mild': [71, 149],
                'moderate': [150, 219],
                'severe': [220, 400],
              },
              clinicalImportance:
                  'PHT is alternative method for calculating MVA (220/PHT). Values >220 ms indicate severe MS. Less accurate in atrial fibrillation and immediate post-valvuloplasty.',
            ),
            _buildModernParameterCard(
              'Mitral Valve VTI',
              'Normal: 8-15 cm • Mild MS: 6-8 cm',
              'Velocity time integral across mitral valve',
              normalRange: [8, 15],
              currentValue: 12,
              unit: 'cm',
              abnormalRanges: {
                'mild': [6, 7],
                'moderate': [4, 5],
                'severe': [2, 3],
              },
              clinicalImportance:
                  'Mitral VTI reflects flow across mitral valve. Reduced VTI indicates mitral stenosis. Used in continuity equation for quantitative assessment.',
            ),
            _buildModernParameterCard(
              'PISA Radius for MR',
              'Normal: <0.4 cm • Mild: 0.4-0.7 cm',
              'Radius of proximal isovelocity surface area',
              normalRange: [0.0, 0.4],
              currentValue: 0.3,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.4, 0.7],
                'moderate': [0.7, 0.9],
                'severe': [0.9, 2.0],
              },
              clinicalImportance:
                  'PISA radius is used to calculate EROA and regurgitant volume. Values >0.9 cm indicate severe MR. Most accurate method for MR quantification.',
            ),
            _buildModernParameterCard(
              'Mitral Inflow Deceleration Time',
              'Normal: 150-240 ms • Age-dependent',
              'Time for E-wave velocity to decrease to baseline',
              normalRange: [150, 240],
              currentValue: 200,
              unit: 'ms',
              abnormalRanges: {
                'mild': [241, 280],
                'moderate': [100, 149],
                'severe': [50, 99],
              },
              clinicalImportance:
                  'Mitral DT reflects LA pressure and LV compliance. Shortened DT indicates elevated LA pressure. Prolonged DT suggests impaired LV relaxation.',
            ),
          ]),

          _buildModernSectionHeader(
            'Aortic Valve Assessment',
            FontAwesomeIcons.heartCircleCheck,
            [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
            'aorticValve',
          ),
          _buildCollapsibleSection('aorticValve', [
            _buildModernParameterCard(
              'Aortic Valve Area (AVA)',
              'Normal: 3-4 cm² • Mild AS: 1.5-2.0 cm²',
              'Measured by continuity equation or planimetry',
              normalRange: [3.0, 4.0],
              currentValue: 3.5,
              unit: 'cm²',
              abnormalRanges: {
                'mild': [1.5, 2.0],
                'moderate': [1.0, 1.4],
                'severe': [0.6, 0.9],
              },
              clinicalImportance:
                  'AVA is the gold standard for assessing aortic stenosis severity. Values <1.0 cm² indicate severe AS requiring intervention. Critical for timing of valve replacement.',
            ),
            _buildModernParameterCard(
              'Aortic Valve Peak Velocity',
              'Normal: <2.5 m/s • Mild AS: 2.5-3.0 m/s',
              'Peak velocity across aortic valve',
              normalRange: [1.0, 2.5],
              currentValue: 1.8,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [2.5, 3.0],
                'moderate': [3.0, 4.0],
                'severe': [4.0, 7.0],
              },
              clinicalImportance:
                  'Peak velocity reflects severity of aortic stenosis. Values >4.0 m/s indicate severe AS. Easy to measure and highly reproducible parameter.',
            ),
            _buildModernParameterCard(
              'Aortic Valve Mean Gradient',
              'Normal: <10 mmHg • Mild AS: 10-20 mmHg',
              'Mean pressure gradient across aortic valve',
              normalRange: [2, 10],
              currentValue: 6,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [10, 20],
                'moderate': [20, 40],
                'severe': [40, 100],
              },
              clinicalImportance:
                  'Mean gradient reflects hemodynamic severity of AS. Values >40 mmHg indicate severe AS. Important for assessing functional impact and symptoms.',
            ),
            _buildModernParameterCard(
              'Aortic Regurgitation Vena Contracta',
              'Normal: <0.3 cm • Mild: 0.3-0.5 cm',
              'Narrowest portion of AR jet',
              normalRange: [0.0, 0.3],
              currentValue: 0.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.3, 0.5],
                'moderate': [0.5, 0.6],
                'severe': [0.6, 1.2],
              },
              clinicalImportance:
                  'Vena contracta is the most reliable parameter for grading AR severity. Values ≥0.6 cm indicate severe AR. Critical for timing of surgical intervention.',
            ),
            _buildModernParameterCard(
              'Aortic Root Diameter',
              'Normal: 2.0-3.7 cm • BSA indexed: <2.1 cm/m²',
              'Measured at sinuses of Valsalva',
              normalRange: [2.0, 3.7],
              currentValue: 3.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [3.8, 4.2],
                'moderate': [4.3, 4.7],
                'severe': [4.8, 6.0],
              },
              clinicalImportance:
                  'Aortic root diameter assessment is crucial for detecting aortic root dilatation. Enlarged root may require surgical intervention to prevent dissection. Important for Marfan syndrome screening.',
            ),
            _buildModernParameterCard(
              'LVOT Diameter',
              'Normal: Men 1.8-2.4 cm • Women 1.6-2.2 cm',
              'Measured in parasternal long axis view',
              normalRange: [1.6, 2.4],
              currentValue: 2.0,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.5, 2.8],
                'moderate': [2.9, 3.2],
                'severe': [3.3, 4.0],
              },
              clinicalImportance:
                  'LVOT diameter is essential for continuity equation calculation of AVA. Accurate measurement is critical for AS severity assessment. Small errors significantly affect AVA calculation.',
            ),
            _buildModernParameterCard(
              'LVOT VTI',
              'Normal: 18-22 cm • Reduced in AS',
              'Velocity time integral in left ventricular outflow tract',
              normalRange: [18, 22],
              currentValue: 20,
              unit: 'cm',
              abnormalRanges: {
                'mild': [15, 17],
                'moderate': [12, 14],
                'severe': [8, 11],
              },
              clinicalImportance:
                  'LVOT VTI is used in continuity equation for AVA calculation. Reduced LVOT VTI may indicate reduced stroke volume or AS. Essential for accurate AS assessment.',
            ),
            _buildModernParameterCard(
              'Aortic Valve VTI',
              'Normal: 18-26 cm • Reduced in AS',
              'Velocity time integral across aortic valve',
              normalRange: [18, 26],
              currentValue: 22,
              unit: 'cm',
              abnormalRanges: {
                'mild': [15, 17],
                'moderate': [12, 14],
                'severe': [8, 11],
              },
              clinicalImportance:
                  'Aortic VTI reflects flow across aortic valve. Used in continuity equation and stroke volume calculation. Reduced in AS and low-flow states.',
            ),
            _buildModernParameterCard(
              'Dimensionless Index (DVI)',
              'Normal: >0.5 • Mild AS: 0.25-0.5',
              'LVOT VTI / Aortic VTI ratio (flow-independent)',
              normalRange: [0.5, 1.0],
              currentValue: 0.8,
              unit: '',
              abnormalRanges: {
                'mild': [0.25, 0.49],
                'moderate': [0.15, 0.24],
                'severe': [0.05, 0.14],
              },
              clinicalImportance:
                  'DVI is flow-independent parameter for AS assessment. Values <0.25 indicate severe AS. Useful when stroke volume is reduced or in low-gradient AS.',
            ),
            _buildModernParameterCard(
              'Aortic Regurgitation Pressure Half-Time',
              'Normal: N/A • Mild AR: >500 ms',
              'Time for AR pressure gradient to decrease by half',
              normalRange: [500, 1000],
              currentValue: 600,
              unit: 'ms',
              abnormalRanges: {
                'mild': [300, 499],
                'moderate': [200, 299],
                'severe': [100, 199],
              },
              clinicalImportance:
                  'AR PHT reflects AR severity and aortic-LV pressure equalization. Values <200 ms indicate severe AR. Useful for AR quantification when other methods are limited.',
            ),
            _buildModernParameterCard(
              'Descending Aorta Diastolic Flow Reversal',
              'Normal: Absent • Mild AR: Brief reversal',
              'Presence and duration of diastolic flow reversal',
              normalRange: [0, 10],
              currentValue: 5,
              unit: '%',
              abnormalRanges: {
                'mild': [10, 30],
                'moderate': [30, 60],
                'severe': [60, 100],
              },
              clinicalImportance:
                  'Diastolic flow reversal in descending aorta indicates AR severity. Holodiastolic reversal suggests severe AR. Important supportive parameter for AR assessment.',
            ),
          ]),

          _buildModernSectionHeader(
            'Tricuspid Valve Assessment',
            FontAwesomeIcons.heartPulse,
            [Color(0xFF4ECDC4), Color(0xFF6EE7E0)],
            'tricuspidValve',
          ),
          _buildCollapsibleSection('tricuspidValve', [
            _buildModernParameterCard(
              'Tricuspid Regurgitation Vena Contracta',
              'Normal: <0.3 cm • Mild: 0.3-0.6 cm',
              'Narrowest portion of TR jet',
              normalRange: [0.0, 0.3],
              currentValue: 0.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.3, 0.6],
                'moderate': [0.7, 0.9],
                'severe': [1.0, 2.0],
              },
              clinicalImportance:
                  'TR vena contracta is the primary parameter for grading TR severity. Values ≥0.7 cm indicate severe TR. Important for timing of tricuspid valve intervention.',
            ),
            _buildModernParameterCard(
              'Tricuspid Regurgitation Volume',
              'Normal: <30 mL • Mild: 30-44 mL',
              'Volume of blood regurgitating per beat',
              normalRange: [0, 30],
              currentValue: 15,
              unit: 'mL',
              abnormalRanges: {
                'mild': [30, 44],
                'moderate': [45, 59],
                'severe': [60, 120],
              },
              clinicalImportance:
                  'TR regurgitant volume quantifies TR severity. Values ≥45 mL indicate significant TR. Important for assessing hemodynamic impact and right heart function.',
            ),
            _buildModernParameterCard(
              'Tricuspid Effective Regurgitant Orifice Area',
              'Normal: <0.2 cm² • Mild: 0.2-0.39 cm²',
              'Effective area of tricuspid regurgitant orifice',
              normalRange: [0.0, 0.2],
              currentValue: 0.1,
              unit: 'cm²',
              abnormalRanges: {
                'mild': [0.2, 0.39],
                'moderate': [0.4, 0.59],
                'severe': [0.6, 1.5],
              },
              clinicalImportance:
                  'TR EROA provides accurate quantification of TR severity. Values ≥0.4 cm² indicate severe TR. Essential for surgical decision-making.',
            ),
            _buildModernParameterCard(
              'Tricuspid Stenosis Mean Gradient',
              'Normal: <2 mmHg • Mild TS: 2-5 mmHg',
              'Mean pressure gradient across tricuspid valve',
              normalRange: [0, 2],
              currentValue: 1,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [2, 5],
                'moderate': [5, 10],
                'severe': [10, 20],
              },
              clinicalImportance:
                  'Tricuspid stenosis is rare but important to detect. Elevated gradients indicate TS severity. May be associated with rheumatic heart disease or carcinoid syndrome.',
            ),
            _buildModernParameterCard(
              'Tricuspid Valve VTI',
              'Normal: 10-15 cm • Reduced in TS',
              'Velocity time integral across tricuspid valve',
              normalRange: [10, 15],
              currentValue: 12,
              unit: 'cm',
              abnormalRanges: {
                'mild': [8, 9],
                'moderate': [6, 7],
                'severe': [3, 5],
              },
              clinicalImportance:
                  'Tricuspid VTI reflects flow across tricuspid valve. Reduced VTI indicates tricuspid stenosis. Used for stroke volume calculation and flow assessment.',
            ),
            _buildModernParameterCard(
              'PISA Radius for TR',
              'Normal: <0.5 cm • Mild: 0.5-0.8 cm',
              'Radius of proximal isovelocity surface area for TR',
              normalRange: [0.0, 0.5],
              currentValue: 0.3,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.5, 0.8],
                'moderate': [0.9, 1.2],
                'severe': [1.3, 2.5],
              },
              clinicalImportance:
                  'PISA radius for TR is used to calculate EROA and regurgitant volume. Values >0.9 cm indicate severe TR. Most accurate method for TR quantification.',
            ),
          ]),

          _buildModernSectionHeader(
            'Pulmonary Valve Assessment',
            FontAwesomeIcons.lungs,
            [Color(0xFFFF9500), Color(0xFFFFB347)],
            'pulmonaryValve',
          ),
          _buildCollapsibleSection('pulmonaryValve', [
            _buildModernParameterCard(
              'Pulmonary Valve Peak Velocity',
              'Normal: <1.5 m/s • Mild PS: 1.5-2.5 m/s',
              'Peak velocity across pulmonary valve',
              normalRange: [0.6, 1.5],
              currentValue: 1.0,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [1.5, 2.5],
                'moderate': [2.5, 4.0],
                'severe': [4.0, 6.0],
              },
              clinicalImportance:
                  'PV peak velocity reflects pulmonary stenosis severity. Values >4.0 m/s indicate severe PS requiring intervention. Important in congenital heart disease.',
            ),
            _buildModernParameterCard(
              'Pulmonary Stenosis Mean Gradient',
              'Normal: <10 mmHg • Mild PS: 10-25 mmHg',
              'Mean pressure gradient across pulmonary valve',
              normalRange: [2, 10],
              currentValue: 6,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [10, 25],
                'moderate': [25, 50],
                'severe': [50, 100],
              },
              clinicalImportance:
                  'Mean gradient quantifies PS severity. Values >50 mmHg indicate severe PS. Critical for timing of balloon valvuloplasty or surgical intervention.',
            ),
            _buildModernParameterCard(
              'Pulmonary Regurgitation Severity',
              'Normal: Trivial/Mild • Moderate: Jet width 25-65%',
              'Assessed by color Doppler jet characteristics',
              normalRange: [0, 25],
              currentValue: 15,
              unit: '%',
              abnormalRanges: {
                'mild': [25, 45],
                'moderate': [45, 65],
                'severe': [65, 100],
              },
              clinicalImportance:
                  'PR severity assessment is important in congenital heart disease and post-surgical patients. Severe PR may lead to RV dysfunction and require intervention.',
            ),
            _buildModernParameterCard(
              'Pulmonary Valve Annulus Diameter',
              'Normal: 1.8-2.4 cm • BSA indexed: 1.0-1.4 cm/m²',
              'Measured in parasternal short axis view',
              normalRange: [1.8, 2.4],
              currentValue: 2.1,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.5, 2.8],
                'moderate': [2.9, 3.2],
                'severe': [3.3, 4.0],
              },
              clinicalImportance:
                  'PV annulus diameter is important for assessing valve size and planning interventions. Enlarged annulus may contribute to PR severity.',
            ),
            _buildModernParameterCard(
              'Pulmonary Valve VTI',
              'Normal: 12-18 cm • Reduced in PS',
              'Velocity time integral across pulmonary valve',
              normalRange: [12, 18],
              currentValue: 15,
              unit: 'cm',
              abnormalRanges: {
                'mild': [10, 11],
                'moderate': [8, 9],
                'severe': [5, 7],
              },
              clinicalImportance:
                  'Pulmonary VTI reflects flow across pulmonary valve. Reduced VTI indicates pulmonary stenosis. Used for stroke volume calculation and RV output assessment.',
            ),
            _buildModernParameterCard(
              'Pulmonary Regurgitation Pressure Half-Time',
              'Normal: N/A • Mild PR: >100 ms',
              'Time for PR pressure gradient to decrease by half',
              normalRange: [100, 200],
              currentValue: 150,
              unit: 'ms',
              abnormalRanges: {
                'mild': [80, 99],
                'moderate': [60, 79],
                'severe': [30, 59],
              },
              clinicalImportance:
                  'PR PHT reflects PR severity and PA-RV pressure equalization. Values <60 ms indicate severe PR. Useful for PR quantification in congenital heart disease.',
            ),
            _buildModernParameterCard(
              'Pulmonary Regurgitation Vena Contracta',
              'Normal: <0.3 cm • Mild: 0.3-0.5 cm',
              'Narrowest portion of PR jet',
              normalRange: [0.0, 0.3],
              currentValue: 0.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [0.3, 0.5],
                'moderate': [0.5, 0.7],
                'severe': [0.7, 1.5],
              },
              clinicalImportance:
                  'PR vena contracta is the most reliable parameter for grading PR severity. Values ≥0.7 cm indicate severe PR. Important in post-surgical congenital heart disease.',
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildAortaContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildModernSectionHeader(
            'Aortic Root Assessment',
            FontAwesomeIcons.heartCircleExclamation,
            [Color(0xFFFF6B6B), Color(0xFFFF8E8E)],
            'aorticRoot',
          ),
          _buildCollapsibleSection('aorticRoot', [
            _buildModernParameterCard(
              'Aortic Root Diameter (Sinuses of Valsalva)',
              'Normal: 2.0-3.7 cm • BSA indexed: <2.1 cm/m²',
              'Measured at maximum diameter of sinuses',
              normalRange: [2.0, 3.7],
              currentValue: 3.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [3.8, 4.2],
                'moderate': [4.3, 4.7],
                'severe': [4.8, 6.0],
              },
              clinicalImportance:
                  'Aortic root dilatation >4.5 cm may require surgical intervention. Values >5.0 cm have high risk of dissection. Critical for Marfan syndrome and bicuspid aortic valve surveillance.',
            ),
            _buildModernParameterCard(
              'Sinotubular Junction Diameter',
              'Normal: 1.8-3.2 cm • BSA indexed: <1.8 cm/m²',
              'Measured at junction of sinuses and ascending aorta',
              normalRange: [1.8, 3.2],
              currentValue: 2.8,
              unit: 'cm',
              abnormalRanges: {
                'mild': [3.3, 3.7],
                'moderate': [3.8, 4.2],
                'severe': [4.3, 5.5],
              },
              clinicalImportance:
                  'STJ dilatation may cause aortic regurgitation. Important for planning valve-sparing aortic root surgery. Ratio to aortic root helps determine surgical approach.',
            ),
            _buildModernParameterCard(
              'Aortic Annulus Diameter',
              'Normal: 1.8-2.6 cm • BSA indexed: <1.5 cm/m²',
              'Measured at hinge points of aortic leaflets',
              normalRange: [1.8, 2.6],
              currentValue: 2.2,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.7, 3.0],
                'moderate': [3.1, 3.5],
                'severe': [3.6, 4.5],
              },
              clinicalImportance:
                  'Aortic annulus diameter is critical for valve sizing in TAVR and surgical valve replacement. Accurate measurement essential for prosthetic valve selection.',
            ),
            _buildModernParameterCard(
              'Left Coronary Cusp Height',
              'Normal: 1.3-2.0 cm • Symmetric with other cusps',
              'Height of left coronary cusp from annulus to tip',
              normalRange: [1.3, 2.0],
              currentValue: 1.6,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.1, 2.4],
                'moderate': [2.5, 2.8],
                'severe': [2.9, 3.5],
              },
              clinicalImportance:
                  'Cusp height assessment important for bicuspid aortic valve evaluation and valve-sparing surgery planning. Asymmetric cusps may indicate congenital abnormalities.',
            ),
            _buildModernParameterCard(
              'Right Coronary Cusp Height',
              'Normal: 1.3-2.0 cm • Symmetric with other cusps',
              'Height of right coronary cusp from annulus to tip',
              normalRange: [1.3, 2.0],
              currentValue: 1.6,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.1, 2.4],
                'moderate': [2.5, 2.8],
                'severe': [2.9, 3.5],
              },
              clinicalImportance:
                  'RCC height evaluation important for bicuspid valve assessment. Prolapse or restriction affects valve function and surgical planning.',
            ),
          ]),

          _buildModernSectionHeader(
            'Ascending Aorta Assessment',
            FontAwesomeIcons.arrowUp,
            [Color(0xFF9B59B6), Color(0xFFBB7FD1)],
            'ascendingAorta',
          ),
          _buildCollapsibleSection('ascendingAorta', [
            _buildModernParameterCard(
              'Ascending Aorta Diameter (Mid-Level)',
              'Normal: 2.1-3.4 cm • BSA indexed: <2.1 cm/m²',
              'Measured at mid-ascending aorta level in parasternal long axis',
              normalRange: [2.1, 3.4],
              currentValue: 2.8,
              unit: 'cm',
              abnormalRanges: {
                'mild': [3.5, 4.0],
                'moderate': [4.1, 4.5],
                'severe': [4.6, 6.0],
              },
              clinicalImportance:
                  'Ascending aorta dilatation >4.5 cm may require surgical intervention. Values >5.5 cm have high dissection risk. Important in bicuspid aortic valve and connective tissue disorders.',
            ),
            _buildModernParameterCard(
              'Aortic Flow Velocity (Ascending)',
              'Normal: 1.0-1.7 m/s • Age-dependent',
              'Peak systolic velocity in ascending aorta by Doppler',
              normalRange: [1.0, 1.7],
              currentValue: 1.3,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [1.8, 2.2],
                'moderate': [2.3, 2.8],
                'severe': [2.9, 4.0],
              },
              clinicalImportance:
                  'Elevated flow velocity may indicate aortic stenosis, coarctation, or hyperdynamic circulation. Important for detecting flow acceleration and obstruction.',
            ),
          ]),

          _buildModernSectionHeader(
            'Aortic Arch Assessment',
            FontAwesomeIcons.bridge,
            [Color(0xFF4ECDC4), Color(0xFF6EE7E0)],
            'aorticArch',
          ),
          _buildCollapsibleSection('aorticArch', [
            _buildModernParameterCard(
              'Aortic Arch Diameter',
              'Normal: 2.0-3.0 cm • BSA indexed: <1.8 cm/m²',
              'Measured in suprasternal notch view',
              normalRange: [2.0, 3.0],
              currentValue: 2.5,
              unit: 'cm',
              abnormalRanges: {
                'mild': [3.1, 3.5],
                'moderate': [3.6, 4.0],
                'severe': [4.1, 5.5],
              },
              clinicalImportance:
                  'Aortic arch dilatation may indicate systemic hypertension or connective tissue disorder. Limited echo visualization but important when visible.',
            ),
            _buildModernParameterCard(
              'Coarctation Gradient (Doppler)',
              'Normal: <20 mmHg • Mild: 20-40 mmHg',
              'Peak instantaneous gradient by continuous wave Doppler',
              normalRange: [0, 20],
              currentValue: 10,
              unit: 'mmHg',
              abnormalRanges: {
                'mild': [20, 40],
                'moderate': [40, 60],
                'severe': [60, 120],
              },
              clinicalImportance:
                  'Coarctation gradient >20 mmHg indicates significant obstruction. Values >40 mmHg typically require intervention. Echo can detect but may underestimate severity.',
            ),
          ]),

          _buildModernSectionHeader(
            'Descending Aorta Assessment',
            FontAwesomeIcons.arrowDown,
            [Color(0xFF2ECC71), Color(0xFF58D68D)],
            'descendingAorta',
          ),
          _buildCollapsibleSection('descendingAorta', [
            _buildModernParameterCard(
              'Descending Aorta Diameter',
              'Normal: 1.6-2.4 cm • BSA indexed: <1.6 cm/m²',
              'Measured in suprasternal or subcostal view',
              normalRange: [1.6, 2.4],
              currentValue: 2.0,
              unit: 'cm',
              abnormalRanges: {
                'mild': [2.5, 3.0],
                'moderate': [3.1, 3.5],
                'severe': [3.6, 5.0],
              },
              clinicalImportance:
                  'Descending aorta dilatation may indicate aortic pathology. Echo visualization limited but important when measurable.',
            ),
            _buildModernParameterCard(
              'Descending Aorta Flow Velocity',
              'Normal: 0.9-1.6 m/s • Age-dependent',
              'Peak systolic velocity by pulsed wave Doppler',
              normalRange: [0.9, 1.6],
              currentValue: 1.2,
              unit: 'm/s',
              abnormalRanges: {
                'mild': [1.7, 2.0],
                'moderate': [2.1, 2.5],
                'severe': [2.6, 4.0],
              },
              clinicalImportance:
                  'Elevated velocity may indicate coarctation or aortic stenosis. Reduced velocity may suggest aortic regurgitation or low cardiac output.',
            ),
            _buildModernParameterCard(
              'Diastolic Flow Reversal (AR Assessment)',
              'Normal: Absent • Mild AR: Brief reversal',
              'Presence and extent of diastolic flow reversal',
              normalRange: [0, 10],
              currentValue: 5,
              unit: '% of cycle',
              abnormalRanges: {
                'mild': [10, 30],
                'moderate': [30, 60],
                'severe': [60, 100],
              },
              clinicalImportance:
                  'Diastolic flow reversal indicates aortic regurgitation severity. Holodiastolic reversal suggests severe AR. Important supportive parameter for AR assessment.',
            ),
          ]),

          _buildModernSectionHeader(
            'Aortic Pathology Assessment',
            FontAwesomeIcons.triangleExclamation,
            [Color(0xFFE74C3C), Color(0xFFEC7063)],
            'aorticPathology',
          ),
          _buildCollapsibleSection('aorticPathology', [
            _buildModernParameterCard(
              'Aortic Coarctation Index',
              'Normal: >0.8 • Mild: 0.6-0.8',
              'Ratio of coarctation site to descending aorta diameter',
              normalRange: [0.8, 1.0],
              currentValue: 0.9,
              unit: '',
              abnormalRanges: {
                'mild': [0.6, 0.79],
                'moderate': [0.4, 0.59],
                'severe': [0.1, 0.39],
              },
              clinicalImportance:
                  'Coarctation index quantifies severity of aortic narrowing. Values <0.5 indicate severe coarctation requiring intervention. Echo can assess when visible.',
            ),
            _buildModernParameterCard(
              'Aortic Regurgitation Jet Assessment',
              'Normal: Absent • Mild: <25% LVOT width',
              'Color Doppler assessment of AR jet characteristics',
              normalRange: [0, 10],
              currentValue: 5,
              unit: '% LVOT width',
              abnormalRanges: {
                'mild': [10, 25],
                'moderate': [25, 65],
                'severe': [65, 100],
              },
              clinicalImportance:
                  'AR jet width relative to LVOT provides semi-quantitative AR assessment. Complements vena contracta and other AR parameters. Easily assessed by echo.',
            ),
          ]),
        ],
      ),
    );
  }

  Widget _buildModernSectionHeader(
    String title,
    IconData icon,
    List<Color> gradient,
    String sectionKey,
  ) {
    final isExpanded = _expandedSections[sectionKey] ?? false;
    final cacheKey = 'header_${sectionKey}_$isExpanded';

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: GestureDetector(
        onTap: () {
          setState(() {
            _expandedSections[sectionKey] = !isExpanded;
          });
        },
        child: _buildGlassmorphicContainer(
          cacheKey: cacheKey,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: gradient[0].withValues(alpha: 0.8),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: gradient[0].withValues(alpha: 0.3),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Icon(icon, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: FontSizes.heading3,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Transform.rotate(
                angle: isExpanded ? 3.14159 : 0.0,
                child: Icon(
                  Icons.keyboard_arrow_down,
                  color: Colors.white70,
                  size: 28,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCollapsibleSection(String sectionKey, dynamic children) {
    final isExpanded = _expandedSections[sectionKey] ?? false;

    return isExpanded
        ? Column(
          children: [
            ...(children is Function ? children() : children),
            const SizedBox(height: 20),
          ],
        )
        : const SizedBox.shrink();
  }

  Widget _buildModernParameterCard(
    String title,
    String normalValues,
    String additionalInfo, {
    required List<double> normalRange,
    required double currentValue,
    required String unit,
    Map<String, List<double>>? abnormalRanges,
    String? clinicalImportance,
  }) {
    final cacheKey = 'param_${title.hashCode}_${normalValues.hashCode}';

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: _buildGlassmorphicContainer(
        cacheKey: cacheKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: FontSizes.bodyLarge,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),

            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'Normal Values',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: FontSizes.bodySmall,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    normalValues,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: FontSizes.bodySmall,
                    ),
                  ),
                ],
              ),
            ),

            if (abnormalRanges != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.orange.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Abnormal Values',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: FontSizes.bodySmall,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    // Handle standard mild/moderate/severe keys
                    if (abnormalRanges['mild'] != null)
                      _buildAbnormalRange(
                        'Mild',
                        abnormalRanges['mild']!,
                        unit,
                        Colors.yellow.shade700,
                      ),
                    if (abnormalRanges['moderate'] != null)
                      _buildAbnormalRange(
                        'Moderate',
                        abnormalRanges['moderate']!,
                        unit,
                        Colors.orange,
                      ),
                    if (abnormalRanges['severe'] != null)
                      _buildAbnormalRange(
                        'Severe',
                        abnormalRanges['severe']!,
                        unit,
                        Colors.red,
                      ),
                    // Handle gender-specific keys
                    if (abnormalRanges['mild_female'] != null)
                      _buildAbnormalRange(
                        'Mild (Female)',
                        abnormalRanges['mild_female']!,
                        unit,
                        Colors.yellow.shade700,
                      ),
                    if (abnormalRanges['moderate_female'] != null)
                      _buildAbnormalRange(
                        'Moderate (Female)',
                        abnormalRanges['moderate_female']!,
                        unit,
                        Colors.orange,
                      ),
                    if (abnormalRanges['severe_female'] != null)
                      _buildAbnormalRange(
                        'Severe (Female)',
                        abnormalRanges['severe_female']!,
                        unit,
                        Colors.red,
                      ),
                    if (abnormalRanges['mild_male'] != null)
                      _buildAbnormalRange(
                        'Mild (Male)',
                        abnormalRanges['mild_male']!,
                        unit,
                        Colors.yellow.shade700,
                      ),
                    if (abnormalRanges['moderate_male'] != null)
                      _buildAbnormalRange(
                        'Moderate (Male)',
                        abnormalRanges['moderate_male']!,
                        unit,
                        Colors.orange,
                      ),
                    if (abnormalRanges['severe_male'] != null)
                      _buildAbnormalRange(
                        'Severe (Male)',
                        abnormalRanges['severe_male']!,
                        unit,
                        Colors.red,
                      ),
                    // Handle alternative naming patterns
                    if (abnormalRanges['mildly_abnormal_women'] != null)
                      _buildAbnormalRange(
                        'Mild (Women)',
                        abnormalRanges['mildly_abnormal_women']!,
                        unit,
                        Colors.yellow.shade700,
                      ),
                    if (abnormalRanges['moderately_abnormal_women'] != null)
                      _buildAbnormalRange(
                        'Moderate (Women)',
                        abnormalRanges['moderately_abnormal_women']!,
                        unit,
                        Colors.orange,
                      ),
                    if (abnormalRanges['severely_abnormal_women'] != null)
                      _buildAbnormalRange(
                        'Severe (Women)',
                        abnormalRanges['severely_abnormal_women']!,
                        unit,
                        Colors.red,
                      ),
                    if (abnormalRanges['mildly_abnormal_men'] != null)
                      _buildAbnormalRange(
                        'Mild (Men)',
                        abnormalRanges['mildly_abnormal_men']!,
                        unit,
                        Colors.yellow.shade700,
                      ),
                    if (abnormalRanges['moderately_abnormal_men'] != null)
                      _buildAbnormalRange(
                        'Moderate (Men)',
                        abnormalRanges['moderately_abnormal_men']!,
                        unit,
                        Colors.orange,
                      ),
                    if (abnormalRanges['severely_abnormal_men'] != null)
                      _buildAbnormalRange(
                        'Severe (Men)',
                        abnormalRanges['severely_abnormal_men']!,
                        unit,
                        Colors.red,
                      ),
                    // Handle age-specific keys
                    if (abnormalRanges['mild_young'] != null)
                      _buildAbnormalRange(
                        'Mild (Young)',
                        abnormalRanges['mild_young']!,
                        unit,
                        Colors.yellow.shade700,
                      ),
                    if (abnormalRanges['moderate_young'] != null)
                      _buildAbnormalRange(
                        'Moderate (Young)',
                        abnormalRanges['moderate_young']!,
                        unit,
                        Colors.orange,
                      ),
                    if (abnormalRanges['severe_young'] != null)
                      _buildAbnormalRange(
                        'Severe (Young)',
                        abnormalRanges['severe_young']!,
                        unit,
                        Colors.red,
                      ),
                    if (abnormalRanges['mild_elderly'] != null)
                      _buildAbnormalRange(
                        'Mild (Elderly)',
                        abnormalRanges['mild_elderly']!,
                        unit,
                        Colors.yellow.shade700,
                      ),
                    if (abnormalRanges['moderate_elderly'] != null)
                      _buildAbnormalRange(
                        'Moderate (Elderly)',
                        abnormalRanges['moderate_elderly']!,
                        unit,
                        Colors.orange,
                      ),
                    if (abnormalRanges['severe_elderly'] != null)
                      _buildAbnormalRange(
                        'Severe (Elderly)',
                        abnormalRanges['severe_elderly']!,
                        unit,
                        Colors.red,
                      ),
                    // Handle location-specific keys
                    if (abnormalRanges['abnormal_septal'] != null)
                      _buildAbnormalRange(
                        'Abnormal (Septal)',
                        abnormalRanges['abnormal_septal']!,
                        unit,
                        Colors.red,
                      ),
                    if (abnormalRanges['abnormal_lateral'] != null)
                      _buildAbnormalRange(
                        'Abnormal (Lateral)',
                        abnormalRanges['abnormal_lateral']!,
                        unit,
                        Colors.red,
                      ),
                    // Handle general abnormal key
                    if (abnormalRanges['abnormal'] != null)
                      _buildAbnormalRange(
                        'Abnormal',
                        abnormalRanges['abnormal']!,
                        unit,
                        Colors.red,
                      ),
                    // Handle intermediate/elevated keys
                    if (abnormalRanges['intermediate'] != null)
                      _buildAbnormalRange(
                        'Intermediate',
                        abnormalRanges['intermediate']!,
                        unit,
                        Colors.orange,
                      ),
                    if (abnormalRanges['elevated'] != null)
                      _buildAbnormalRange(
                        'Elevated',
                        abnormalRanges['elevated']!,
                        unit,
                        Colors.red,
                      ),
                  ],
                ),
              ),
            ],

            if (clinicalImportance != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.blue, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'Clinical Importance',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: FontSizes.bodySmall,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      clinicalImportance,
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: FontSizes.bodySmall,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            if (additionalInfo.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                additionalInfo,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: FontSizes.bodySmall,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAbnormalRange(
    String severity,
    List<double> range,
    String unit,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 8),
          Text(
            '$severity: ${range[0]} - ${range[1]} $unit',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: FontSizes.bodySmall,
            ),
          ),
        ],
      ),
    );
  }
}
