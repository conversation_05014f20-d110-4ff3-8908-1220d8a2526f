import 'base_interpretation.dart';

class MitralValveInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Mitral valve assessment was not performed.';
    }

    final valveInfo = data['valves'] ?? {};

    final morphologyMap = valveInfo['morphology'] ?? {};
    final mitralMorphology = morphologyMap['Mitral'] as String? ?? 'Normal';

    final morphologyOtherMap = valveInfo['morphologyOther'] ?? {};
    final mitralMorphologyOther = morphologyOtherMap['Mitral'] as String?;

    final stenosisMap = valveInfo['stenosis'] ?? {};
    final mitralStenosis = stenosisMap['Mitral'] as String? ?? 'None';

    final regurgitationMap = valveInfo['regurgitation'] ?? {};
    final mitralRegurgitation = regurgitationMap['Mitral'] as String? ?? 'None';

    final prostheticInfo = valveInfo['prosthetic'] ?? {};
    final mitralProsthetic = prostheticInfo['mitral'] == true;

    final prostheticTypes = valveInfo['prostheticTypes'] ?? {};
    final mitralProstheticType = prostheticTypes['mitral'] as String?;

    final prostheticFunctions = valveInfo['prostheticFunctions'] ?? {};
    final mitralProstheticFunction = prostheticFunctions['mitral'] as String?;

    final mitralRhythm = valveInfo['mitralRhythm'] as String? ?? 'sinusRhythm';

    final StringBuffer mitralParagraph = StringBuffer('The mitral valve ');

    if (mitralMorphology == 'Prosthetic' || mitralProsthetic) {
      mitralParagraph.write('is prosthetic ');

      if (mitralProstheticType != null) {
        mitralParagraph.write('($mitralProstheticType) ');
      }

      if (mitralProstheticFunction != null) {
        String lowerCaseFunction = mitralProstheticFunction.toLowerCase();

        if (lowerCaseFunction == 'normal') {
          mitralParagraph.write('with normal function ');
        } else if (lowerCaseFunction.startsWith('svd')) {
          final svdType = mitralProstheticFunction.substring(3);
          final formattedSvdType = _formatCamelCase(svdType);
          mitralParagraph.write(
            'with structural valve dysfunction ($formattedSvdType) ',
          );
        } else {
          Map<String, String> dysfunctionDescriptions = {
            'paravalvularleak': 'with paravalvular leak ',
            'ppm': 'with prosthesis-patient mismatch ',
            'pannus': 'with pannus formation ',
            'malposition': 'with malposition ',
            'endocarditis': 'with evidence of endocarditis ',
            'thrombus': 'with thrombus formation ',
          };

          if (dysfunctionDescriptions.containsKey(lowerCaseFunction)) {
            mitralParagraph.write(dysfunctionDescriptions[lowerCaseFunction]!);
          } else {
            final formattedFunction = _formatCamelCase(
              mitralProstheticFunction,
            );
            mitralParagraph.write('with $formattedFunction ');
          }
        }
      }
    } else {
      if (mitralMorphology.toLowerCase() == 'normal') {
        mitralParagraph.write('has normal morphology ');
      } else {
        _addMorphologyDescription(
          mitralParagraph,
          mitralMorphology,
          mitralMorphologyOther,
        );
      }

      if (mitralStenosis.toLowerCase() != 'none' ||
          mitralRegurgitation.toLowerCase() != 'none') {
        mitralParagraph.write('with ');
      } else {
        mitralParagraph.write('with no significant stenosis or regurgitation');
      }
    }

    bool needsComma = false;
    if (mitralStenosis.toLowerCase() != 'none') {
      Map<String, String> stenosisDescriptions = {
        'mild': 'mild mitral stenosis (valve area >1.5 cm²)',
        'moderate': 'moderate mitral stenosis (valve area 1.0-1.5 cm²)',
        'severe': 'severe mitral stenosis (valve area <1.0 cm²)',
        'critical': 'critical mitral stenosis (valve area <0.6 cm²)',
      };

      String lowerCaseStenosis = mitralStenosis.toLowerCase();
      if (stenosisDescriptions.containsKey(lowerCaseStenosis)) {
        mitralParagraph.write(stenosisDescriptions[lowerCaseStenosis]!);
      } else {
        mitralParagraph.write('$mitralStenosis mitral stenosis');
      }

      needsComma = true;
    }

    if (mitralRegurgitation.toLowerCase() != 'none') {
      if (needsComma) mitralParagraph.write(' and ');

      Map<String, String> regurgitationDescriptions = {
        'trace': 'trace mitral regurgitation',
        'mild': 'mild mitral regurgitation',
        'moderate': 'moderate mitral regurgitation',
        'moderatesevere': 'moderate-to-severe mitral regurgitation',
        'severe': 'severe mitral regurgitation',
      };

      String lowerCaseRegurgitation = mitralRegurgitation.toLowerCase();
      if (regurgitationDescriptions.containsKey(lowerCaseRegurgitation)) {
        mitralParagraph.write(
          regurgitationDescriptions[lowerCaseRegurgitation]!,
        );
      } else {
        mitralParagraph.write('$mitralRegurgitation mitral regurgitation');
      }

      needsComma = true;
    }

    if (!mitralParagraph.toString().endsWith('.')) {
      mitralParagraph.write('.');
    }

    if (mitralRhythm.toLowerCase() != 'sinusrhythm') {
      if (mitralParagraph.toString().endsWith('.')) {
        mitralParagraph.write(' The rhythm is ');
      }

      Map<String, String> rhythmDescriptions = {
        'atrialfibrillation': 'atrial fibrillation.',
        'atrialflutter': 'atrial flutter.',
        'ectopicatrialrhythm': 'ectopic atrial rhythm.',
        'junctionalrhythm': 'junctional rhythm.',
        'ventricularrhythm': 'ventricular rhythm.',
        'heartblock': 'heart block.',
        'pacedrhythm': 'paced rhythm.',
      };

      String lowerCaseRhythm = mitralRhythm.toLowerCase();
      if (rhythmDescriptions.containsKey(lowerCaseRhythm)) {
        mitralParagraph.write(rhythmDescriptions[lowerCaseRhythm]!);
      }
    }

    return mitralParagraph.toString();
  }

  void _addMorphologyDescription(
    StringBuffer paragraph,
    String morphology,
    String? morphologyOther,
  ) {
    String lowerCaseMorphology = morphology.toLowerCase();

    Map<String, String> nativeDescriptions = {
      'bicuspid': 'has bicuspid morphology with fusion of the leaflets ',
      'calcified': 'shows calcification of the leaflets and/or annulus ',
      'myxomatous':
          'demonstrates myxomatous degeneration with leaflet thickening and redundancy ',
      'rheumatic':
          'has rheumatic changes with commissural fusion and leaflet thickening ',
      'thrombus': 'has thrombus formation on the leaflets ',
      'vegetation': 'has vegetations attached to the leaflets ',
    };

    if (lowerCaseMorphology == 'other') {
      if (morphologyOther != null && morphologyOther.isNotEmpty) {
        paragraph.write('has abnormal morphology: $morphologyOther ');
      } else {
        paragraph.write('has abnormal morphology ');
      }
    } else if (nativeDescriptions.containsKey(lowerCaseMorphology)) {
      paragraph.write(nativeDescriptions[lowerCaseMorphology]!);
    } else {
      paragraph.write('has $morphology morphology ');
    }
  }

  String _formatCamelCase(String input) {
    if (input.isEmpty) return input;

    final result = input.replaceAllMapped(
      RegExp(r'([a-z])([A-Z])'),
      (match) => '${match.group(1)} ${match.group(2)}',
    );

    return result.substring(0, 1).toUpperCase() + result.substring(1);
  }
}
