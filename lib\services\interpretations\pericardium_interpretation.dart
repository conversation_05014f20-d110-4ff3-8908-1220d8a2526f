import 'base_interpretation.dart';

class PericardiumInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Pericardial assessment was not performed.';
    }

    final pericardium = data['pericardium'] ?? {};
    final effusionSeverity =
        pericardium['effusionSeverity'] as String? ?? 'None';
    final effusionLocation =
        pericardium['effusionLocation'] as String? ?? 'None';

    if (effusionSeverity.toLowerCase() != 'none') {
      String locationDescription = formatEffusionLocation(effusionLocation);
      return 'There is $effusionSeverity pericardial effusion$locationDescription.';
    } else {
      return 'There is no pericardial effusion.';
    }
  }

  String formatEffusionLocation(String location) {
    String lowerCaseLocation = location.toLowerCase();

    if (lowerCaseLocation == 'none') {
      return '';
    } else if (lowerCaseLocation == 'circumferential') {
      return ' that is circumferential in distribution';
    } else if (lowerCaseLocation == 'anterior') {
      return ' located anteriorly';
    } else if (lowerCaseLocation == 'posterior') {
      return ' located posteriorly';
    } else if (lowerCaseLocation == 'lateral') {
      return ' located laterally';
    } else if (lowerCaseLocation == 'apical') {
      return ' located at the apex';
    } else if (lowerCaseLocation == 'loculated') {
      return ' that appears loculated';
    } else {
      return ' located in the $location region';
    }
  }
}
