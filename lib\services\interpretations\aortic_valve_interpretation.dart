import 'base_interpretation.dart';

class AorticValveInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Aortic valve assessment was not performed.';
    }

    final valveInfo = data['valves'] ?? {};

    final morphologyMap = valveInfo['morphology'] ?? {};
    final aorticMorphology = morphologyMap['Aortic'] as String? ?? 'Normal';

    final morphologyOtherMap = valveInfo['morphologyOther'] ?? {};
    final aorticMorphologyOther = morphologyOtherMap['Aortic'] as String?;

    final stenosisMap = valveInfo['stenosis'] ?? {};
    final aorticStenosis = stenosisMap['Aortic'] as String? ?? 'None';

    final regurgitationMap = valveInfo['regurgitation'] ?? {};
    final aorticRegurgitation = regurgitationMap['Aortic'] as String? ?? 'None';

    final prostheticInfo = valveInfo['prosthetic'] ?? {};
    final aorticProsthetic = prostheticInfo['aortic'] == true;

    final prostheticTypes = valveInfo['prostheticTypes'] ?? {};
    final aorticProstheticType = prostheticTypes['aortic'] as String?;

    final prostheticFunctions = valveInfo['prostheticFunctions'] ?? {};
    final aorticProstheticFunction = prostheticFunctions['aortic'] as String?;

    final StringBuffer aorticParagraph = StringBuffer('The aortic valve ');

    if (aorticProsthetic) {
      aorticParagraph.write('is prosthetic ');

      if (aorticProstheticType != null) {
        aorticParagraph.write('($aorticProstheticType) ');
      }

      if (aorticProstheticFunction != null) {
        String lowerCaseFunction = aorticProstheticFunction.toLowerCase();

        if (lowerCaseFunction == 'normal') {
          aorticParagraph.write('with normal function ');
        } else if (lowerCaseFunction.startsWith('svd')) {
          final svdType = aorticProstheticFunction.substring(3);
          final formattedSvdType = _formatCamelCase(svdType);
          aorticParagraph.write(
            'with structural valve dysfunction ($formattedSvdType) ',
          );
        } else {
          Map<String, String> dysfunctionDescriptions = {
            'paravalvularleak': 'with paravalvular leak ',
            'ppm': 'with prosthesis-patient mismatch ',
            'pannus': 'with pannus formation ',
            'malposition': 'with malposition ',
            'endocarditis': 'with evidence of endocarditis ',
            'thrombus': 'with thrombus formation ',
          };

          if (dysfunctionDescriptions.containsKey(lowerCaseFunction)) {
            aorticParagraph.write(dysfunctionDescriptions[lowerCaseFunction]!);
          } else {
            final formattedFunction = _formatCamelCase(
              aorticProstheticFunction,
            );
            aorticParagraph.write('with $formattedFunction ');
          }
        }
      }
    } else {
      if (aorticMorphology.toLowerCase() == 'normal') {
        aorticParagraph.write('has normal morphology ');
      } else {
        _addMorphologyDescription(
          aorticParagraph,
          aorticMorphology,
          aorticMorphologyOther,
          false,
        );
      }

      if (aorticStenosis.toLowerCase() != 'none' ||
          aorticRegurgitation.toLowerCase() != 'none') {
        aorticParagraph.write('with ');
      } else {
        aorticParagraph.write('with no significant stenosis or regurgitation');
      }
    }

    bool needsComma = false;
    if (aorticStenosis.toLowerCase() != 'none') {
      Map<String, String> stenosisDescriptions = {
        'mild': 'mild aortic stenosis (valve area >1.5 cm²)',
        'moderate': 'moderate aortic stenosis (valve area 1.0-1.5 cm²)',
        'severe': 'severe aortic stenosis (valve area <1.0 cm²)',
        'critical': 'critical aortic stenosis (valve area <0.6 cm²)',
      };

      String lowerCaseStenosis = aorticStenosis.toLowerCase();
      if (stenosisDescriptions.containsKey(lowerCaseStenosis)) {
        aorticParagraph.write(stenosisDescriptions[lowerCaseStenosis]!);
      } else {
        aorticParagraph.write('$aorticStenosis aortic stenosis');
      }

      needsComma = true;
    }

    if (aorticRegurgitation.toLowerCase() != 'none') {
      if (needsComma) aorticParagraph.write(' and ');

      Map<String, String> regurgitationDescriptions = {
        'trace': 'trace aortic regurgitation',
        'mild': 'mild aortic regurgitation',
        'moderate': 'moderate aortic regurgitation',
        'moderatesevere': 'moderate-to-severe aortic regurgitation',
        'severe': 'severe aortic regurgitation',
      };

      String lowerCaseRegurgitation = aorticRegurgitation.toLowerCase();
      if (regurgitationDescriptions.containsKey(lowerCaseRegurgitation)) {
        aorticParagraph.write(
          regurgitationDescriptions[lowerCaseRegurgitation]!,
        );
      } else {
        aorticParagraph.write('$aorticRegurgitation aortic regurgitation');
      }

      needsComma = true;
    }

    if (!aorticParagraph.toString().endsWith('.')) {
      aorticParagraph.write('.');
    }

    return aorticParagraph.toString();
  }

  void _addMorphologyDescription(
    StringBuffer paragraph,
    String morphology,
    String? morphologyOther,
    bool isProsthetic,
  ) {
    String lowerCaseMorphology = morphology.toLowerCase();

    Map<String, String> nativeDescriptions = {
      'bicuspid': 'is bicuspid with fusion of the leaflets ',
      'calcified': 'shows calcification of the leaflets and/or annulus ',
      'myxomatous':
          'demonstrates myxomatous degeneration with leaflet thickening and redundancy ',
      'rheumatic':
          'has rheumatic changes with commissural fusion and leaflet thickening ',
      'thrombus': 'has thrombus formation on the leaflets ',
      'vegetation': 'has vegetations attached to the leaflets ',
    };

    if (lowerCaseMorphology == 'other') {
      if (morphologyOther != null && morphologyOther.isNotEmpty) {
        paragraph.write('has abnormal morphology: $morphologyOther ');
      } else {
        paragraph.write('has abnormal morphology ');
      }
    } else if (nativeDescriptions.containsKey(lowerCaseMorphology)) {
      paragraph.write(nativeDescriptions[lowerCaseMorphology]!);
    } else {
      paragraph.write('has $morphology morphology ');
    }
  }

  String _formatCamelCase(String input) {
    if (input.isEmpty) return input;

    final result = input.replaceAllMapped(
      RegExp(r'([a-z])([A-Z])'),
      (match) => '${match.group(1)} ${match.group(2)}',
    );

    return result.substring(0, 1).toUpperCase() + result.substring(1);
  }
}
