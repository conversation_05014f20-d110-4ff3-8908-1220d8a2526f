import 'package:flutter/material.dart';
import 'dart:async';

// ignore: unused_import
import 'package:supabase_flutter/supabase_flutter.dart';
import 'auth/login_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;

  bool _isLoading = true;

  bool _animationCompleted = false;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.65, curve: Curves.easeInOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.6, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.65, curve: Curves.easeOutBack),
      ),
    );

    _controller.forward();

    _checkAuthStatus();

    Timer(const Duration(seconds: 3), () {
      debugPrint('⏰ SplashScreen: 3-second timer completed');
      if (mounted) {
        setState(() {
          _animationCompleted = true;
        });
        _navigateIfReady();
      }
    });
  }

  Future<void> _checkAuthStatus() async {
    debugPrint('🔐 SplashScreen: Starting authentication check...');

    // Set loading to false immediately since we don't need to wait for auth check
    setState(() {
      _isLoading = false;
    });

    // Still call _navigateIfReady to handle navigation when animation is complete
    _navigateIfReady();
  }

  void _navigateIfReady() {
    debugPrint('🚀 SplashScreen: _navigateIfReady called');
    debugPrint('🚀 SplashScreen: _animationCompleted = $_animationCompleted');
    debugPrint('🚀 SplashScreen: _isLoading = $_isLoading');

    if (!_animationCompleted) {
      debugPrint('🚀 SplashScreen: Not ready to navigate yet. Waiting...');
      return;
    }

    if (!mounted) {
      debugPrint('🚀 SplashScreen: Widget not mounted, skipping navigation');
      return;
    }

    // Always navigate to LoginScreen regardless of authentication status
    debugPrint('🚀 SplashScreen: Navigating to LoginScreen');
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => const LoginScreen()),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          color: Color.fromARGB(255, 255, 255, 255),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedBuilder(
                animation: _controller,
                builder: (context, child) {
                  return Opacity(
                    opacity: _opacityAnimation.value,
                    child: Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Image.asset(
                        'lib/assets/images/syvursoft.jpeg',
                        width: 200,
                        height: 200,
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 40),

              if (_animationCompleted && _isLoading)
                const Column(
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Color(0xFF1E88E5),
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Verifying authentication...',
                      style: TextStyle(color: Color(0xFF616161), fontSize: 14),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}
