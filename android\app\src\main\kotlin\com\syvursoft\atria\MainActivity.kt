package com.syvursoft.atria

import android.content.pm.PackageManager
import android.content.pm.Signature
import android.os.Build
import android.os.Bundle
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import java.io.File
import java.security.MessageDigest

class MainActivity : FlutterActivity() {
    private val SECURITY_CHANNEL = "app_security"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Setup app security channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SECURITY_CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "getAppSignatures" -> {
                        result.success(getAppSignatures())
                    }
                    "getInstallerPackageName" -> {
                        result.success(getInstallerPackageName())
                    }
                    "isDeviceRooted" -> {
                        result.success(isDeviceRooted())
                    }
                    "isDebuggingEnabled" -> {
                        result.success(isDebuggingEnabled())
                    }
                    "isDebuggerAttached" -> {
                        result.success(isDebuggerAttached())
                    }
                    else -> result.notImplemented()
                }
            }
    }



    // Security validation methods
    private fun getAppSignatures(): List<String> {
        val signatures = mutableListOf<String>()
        try {
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageManager.getPackageInfo(packageName, PackageManager.GET_SIGNING_CERTIFICATES)
            } else {
                @Suppress("DEPRECATION")
                packageManager.getPackageInfo(packageName, PackageManager.GET_SIGNATURES)
            }

            val sigs = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.signingInfo?.apkContentsSigners
            } else {
                @Suppress("DEPRECATION")
                packageInfo.signatures
            }

            sigs?.forEach { signature ->
                signatures.add(getSignatureFingerprint(signature))
            }
        } catch (e: Exception) {
            // Handle error silently
        }
        return signatures
    }

    private fun getSignatureFingerprint(signature: Signature): String {
        val md = MessageDigest.getInstance("SHA-256")
        md.update(signature.toByteArray())
        return md.digest().joinToString(":") { "%02X".format(it) }
    }

    private fun getInstallerPackageName(): String? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                packageManager.getInstallSourceInfo(packageName).installingPackageName
            } else {
                @Suppress("DEPRECATION")
                packageManager.getInstallerPackageName(packageName)
            }
        } catch (e: Exception) {
            null
        }
    }

    private fun isDeviceRooted(): Boolean {
        val rootFiles = arrayOf(
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su"
        )

        for (file in rootFiles) {
            if (File(file).exists()) {
                return true
            }
        }
        return false
    }

    private fun isDebuggingEnabled(): Boolean {
        return try {
            android.provider.Settings.Secure.getInt(
                contentResolver,
                android.provider.Settings.Global.ADB_ENABLED, 0
            ) == 1
        } catch (e: Exception) {
            false
        }
    }

    private fun isDebuggerAttached(): Boolean {
        return android.os.Debug.isDebuggerConnected()
    }
}
