{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterprojects\\atria\\android\\app\\.cxx\\RelWithDebInfo\\5b6a3x6j\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\flutterprojects\\atria\\android\\app\\.cxx\\RelWithDebInfo\\5b6a3x6j\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}