import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:printing/printing.dart';
import '../models/report_model.dart';
import 'pdf_editor_screen.dart';

class ReportViewerScreen extends StatefulWidget {
  final Report report;

  const ReportViewerScreen({super.key, required this.report});

  @override
  State<ReportViewerScreen> createState() => _ReportViewerScreenState();
}

class _ReportViewerScreenState extends State<ReportViewerScreen> {
  final GlobalKey<SfPdfViewerState> _pdfViewerKey = GlobalKey();

  Future<void> _printPdf() async {
    if (widget.report.pdfBytes == null) return;

    await Printing.layoutPdf(
      onLayout: (format) => widget.report.pdfBytes!,
      name: 'ATRIA ${widget.report.reportType} Report',
    );
  }

  Future<void> _sharePdf() async {
    if (widget.report.pdfBytes == null) return;

    final result = await Printing.sharePdf(
      bytes: widget.report.pdfBytes!,
      filename: 'atria_${widget.report.reportType}_report.pdf',
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result ? 'PDF shared successfully' : 'Failed to share PDF',
          ),
        ),
      );
    }
  }

  Future<void> _editPdf() async {
    if (widget.report.pdfBytes == null) return;

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PdfEditorScreen(report: widget.report),
      ),
    );

    if (result != null && result is Report) {
      // Update the report with edited version
      setState(() {
        // The report will be updated through the parent widget
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('PDF edited successfully')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.report.title),
        backgroundColor:
            widget.report.reportType == 'quick'
                ? Colors.orange.shade800
                : const Color(0xFF1E88E5),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            tooltip: 'Edit',
            onPressed: widget.report.pdfBytes == null ? null : _editPdf,
          ),
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'Print',
            onPressed: widget.report.pdfBytes == null ? null : _printPdf,
          ),
          IconButton(
            icon: const Icon(Icons.share),
            tooltip: 'Share',
            onPressed: widget.report.pdfBytes == null ? null : _sharePdf,
          ),
        ],
      ),
      body:
          widget.report.pdfBytes != null
              ? SfPdfViewer.memory(
                widget.report.pdfBytes!,
                key: _pdfViewerKey,
                canShowScrollHead: true,
                canShowScrollStatus: true,
                enableDoubleTapZooming: true,
                enableTextSelection: true,
                interactionMode: PdfInteractionMode.pan,
                pageLayoutMode: PdfPageLayoutMode.single,
              )
              : Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.error_outline, size: 48, color: Colors.red),
                    SizedBox(height: 16),
                    Text('PDF data not available'),
                  ],
                ),
              ),
    );
  }
}
