import 'package:flutter/material.dart';
import '../services/secure_config_service.dart';

/// Simple utility to set up secure configuration
class SetupSecureConfig {
  static Future<void> setupConfiguration() async {
    // Store the configuration values directly
    await SecureConfigService.storeSupabaseUrl(
      'https://rggpzozsvtreysccgqae.supabase.co',
    );
    await SecureConfigService.storeSupabaseAnonKey(
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJnZ3B6b3pzdnRyZXlzY2NncWFlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4NzAyNTMsImV4cCI6MjA2MzQ0NjI1M30.yuYvTs672FW1O_3ivba-Y17BFUNOi3M1AfiWU2UWDr4',
    );

    debugPrint('✅ Configuration stored in secure storage successfully');
  }
}
