import 'base_interpretation.dart';

class PulmonaryValveInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Pulmonary valve assessment was not performed.';
    }

    final valveInfo = data['valves'] ?? {};

    final morphologyMap = valveInfo['morphology'] ?? {};
    final pulmonaryMorphology =
        morphologyMap['Pulmonary'] as String? ?? 'Normal';

    final morphologyOtherMap = valveInfo['morphologyOther'] ?? {};
    final pulmonaryMorphologyOther = morphologyOtherMap['Pulmonary'] as String?;

    final stenosisMap = valveInfo['stenosis'] ?? {};
    final pulmonaryStenosis = stenosisMap['Pulmonary'] as String? ?? 'None';

    final regurgitationMap = valveInfo['regurgitation'] ?? {};
    final pulmonaryRegurgitation =
        regurgitationMap['Pulmonary'] as String? ?? 'None';

    final prostheticInfo = valveInfo['prosthetic'] ?? {};
    final pulmonaryProsthetic = prostheticInfo['pulmonary'] == true;

    final prostheticTypes = valveInfo['prostheticTypes'] ?? {};
    final pulmonaryProstheticType = prostheticTypes['pulmonary'] as String?;

    final prostheticFunctions = valveInfo['prostheticFunctions'] ?? {};
    final pulmonaryProstheticFunction =
        prostheticFunctions['pulmonary'] as String?;

    final StringBuffer pulmonaryParagraph = StringBuffer(
      'The pulmonary valve ',
    );

    if (pulmonaryProsthetic) {
      pulmonaryParagraph.write('is prosthetic ');

      if (pulmonaryProstheticType != null) {
        pulmonaryParagraph.write('($pulmonaryProstheticType) ');
      }

      if (pulmonaryProstheticFunction != null) {
        String lowerCaseFunction = pulmonaryProstheticFunction.toLowerCase();

        if (lowerCaseFunction == 'normal') {
          pulmonaryParagraph.write('with normal function ');
        } else if (lowerCaseFunction.startsWith('svd')) {
          final svdType = pulmonaryProstheticFunction.substring(3);
          final formattedSvdType = _formatCamelCase(svdType);
          pulmonaryParagraph.write(
            'with structural valve dysfunction ($formattedSvdType) ',
          );
        } else {
          Map<String, String> dysfunctionDescriptions = {
            'paravalvularleak': 'with paravalvular leak ',
            'ppm': 'with prosthesis-patient mismatch ',
            'pannus': 'with pannus formation ',
            'malposition': 'with malposition ',
            'endocarditis': 'with evidence of endocarditis ',
            'thrombus': 'with thrombus formation ',
          };

          if (dysfunctionDescriptions.containsKey(lowerCaseFunction)) {
            pulmonaryParagraph.write(
              dysfunctionDescriptions[lowerCaseFunction]!,
            );
          } else {
            final formattedFunction = _formatCamelCase(
              pulmonaryProstheticFunction,
            );
            pulmonaryParagraph.write('with $formattedFunction ');
          }
        }
      }
    } else {
      if (pulmonaryMorphology.toLowerCase() == 'normal') {
        pulmonaryParagraph.write('has normal morphology ');
      } else {
        _addMorphologyDescription(
          pulmonaryParagraph,
          pulmonaryMorphology,
          pulmonaryMorphologyOther,
        );
      }

      if (pulmonaryStenosis.toLowerCase() != 'none' ||
          pulmonaryRegurgitation.toLowerCase() != 'none') {
        pulmonaryParagraph.write('with ');
      } else {
        pulmonaryParagraph.write(
          'with no significant stenosis or regurgitation',
        );
      }
    }

    bool needsComma = false;
    if (pulmonaryStenosis.toLowerCase() != 'none') {
      Map<String, String> stenosisDescriptions = {
        'mild':
            'mild pulmonary stenosis (peak gradient 20-35 mmHg, peak velocity 2.2-3.0 m/s)',
        'moderate':
            'moderate pulmonary stenosis (peak gradient 36-63 mmHg, peak velocity 3.0-4.0 m/s)',
        'severe':
            'severe pulmonary stenosis (peak gradient ≥64 mmHg, peak velocity >4.0 m/s)',
        'critical':
            'critical pulmonary stenosis (peak gradient >80 mmHg with RV dysfunction)',
      };

      String lowerCaseStenosis = pulmonaryStenosis.toLowerCase();
      if (stenosisDescriptions.containsKey(lowerCaseStenosis)) {
        pulmonaryParagraph.write(stenosisDescriptions[lowerCaseStenosis]!);
      } else {
        pulmonaryParagraph.write('$pulmonaryStenosis pulmonary stenosis');
      }

      needsComma = true;
    }

    if (pulmonaryRegurgitation.toLowerCase() != 'none') {
      if (needsComma) pulmonaryParagraph.write(' and ');

      Map<String, String> regurgitationDescriptions = {
        'trace': 'trace pulmonary regurgitation (physiologic/trivial)',
        'mild': 'mild pulmonary regurgitation (small central jet)',
        'moderate': 'moderate pulmonary regurgitation (intermediate jet size)',
        'moderatesevere': 'moderate-to-severe pulmonary regurgitation',
        'severe':
            'severe pulmonary regurgitation (large central jet or eccentric wall-impinging jet)',
      };

      String lowerCaseRegurgitation = pulmonaryRegurgitation.toLowerCase();
      if (regurgitationDescriptions.containsKey(lowerCaseRegurgitation)) {
        pulmonaryParagraph.write(
          regurgitationDescriptions[lowerCaseRegurgitation]!,
        );
      } else {
        pulmonaryParagraph.write(
          '$pulmonaryRegurgitation pulmonary regurgitation',
        );
      }

      needsComma = true;
    }

    if (!pulmonaryParagraph.toString().endsWith('.')) {
      pulmonaryParagraph.write('.');
    }

    return pulmonaryParagraph.toString();
  }

  void _addMorphologyDescription(
    StringBuffer paragraph,
    String morphology,
    String? morphologyOther,
  ) {
    String lowerCaseMorphology = morphology.toLowerCase();

    Map<String, String> nativeDescriptions = {
      'bicuspid': 'has bicuspid morphology with fusion of the leaflets ',
      'calcified': 'shows calcification of the leaflets and/or annulus ',
      'myxomatous':
          'demonstrates myxomatous degeneration with leaflet thickening and redundancy ',
      'rheumatic':
          'has rheumatic changes with commissural fusion and leaflet thickening ',
      'dysplastic':
          'shows dysplastic changes with thickened, immobile leaflets ',
      'thrombus': 'has thrombus formation on the leaflets ',
      'vegetation': 'has vegetations attached to the leaflets ',
    };

    if (lowerCaseMorphology == 'other') {
      if (morphologyOther != null && morphologyOther.isNotEmpty) {
        paragraph.write('has abnormal morphology: $morphologyOther ');
      } else {
        paragraph.write('has abnormal morphology ');
      }
    } else if (nativeDescriptions.containsKey(lowerCaseMorphology)) {
      paragraph.write(nativeDescriptions[lowerCaseMorphology]!);
    } else {
      paragraph.write('has $morphology morphology ');
    }
  }

  String _formatCamelCase(String input) {
    if (input.isEmpty) return input;

    final result = input.replaceAllMapped(
      RegExp(r'([a-z])([A-Z])'),
      (match) => '${match.group(1)} ${match.group(2)}',
    );

    return result.substring(0, 1).toUpperCase() + result.substring(1);
  }
}
