import 'base_interpretation.dart';

class RightAtriumInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Right atrial assessment was not performed.';
    }

    final echoParams = data['echoParameters'] ?? {};
    final raSize = echoParams['raSize'] as String? ?? 'normal';

    final ivc = data['ivc'] ?? {};
    final ivcDiameter = _parseDouble(ivc['diameter']);
    final ivcCollapsibility = ivc['collapsibility'] as String? ?? 'Normal';

    String raSizeDescription =
        raSize.toLowerCase() == 'dilated' ? 'dilated' : 'normal in size';

    if (ivcDiameter != null) {
      String raPressure = _estimateRAPressure(ivcDiameter, ivcCollapsibility);
      return 'The right atrium is $raSizeDescription with $raPressure pressure.';
    } else {
      return 'The right atrium is $raSizeDescription.';
    }
  }

  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  String _estimateRAPressure(double diameter, String collapsibility) {
    if (diameter <= 2.1 && collapsibility == 'Normal') {
      return 'normal';
    } else if (diameter > 2.1 && collapsibility == 'Reduced') {
      return 'elevated';
    } else if (diameter > 2.1 && collapsibility == 'Absent') {
      return 'significantly elevated';
    } else if (diameter <= 2.1 &&
        (collapsibility == 'Reduced' || collapsibility == 'Absent')) {
      return 'mildly elevated';
    } else {
      return 'indeterminate';
    }
  }
}
