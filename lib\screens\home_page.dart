import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'quick_report_screen.dart';
import 'my_reports_screen.dart';
import 'profile_screen.dart';
import 'echo_reference_screen.dart';
import '../widgets/app_drawer.dart';
import '../widgets/report_card.dart';
import 'smart_templates_screen.dart';
import '../models/user_profile_model.dart';
import '../services/user_profile_service.dart';
import '../constants/font_sizes.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver {
  bool _hasAccess = false;
  bool _checkingAccess = true;

  late Map<String, dynamic> _currentGreeting;

  Key _doctorNameKey = UniqueKey();

  @override
  void initState() {
    super.initState();
    _checkAccess();
    _updateGreeting();

    WidgetsBinding.instance.addObserver(this);
  }

  void _updateGreeting() {
    setState(() {
      _currentGreeting = _getRandomGreetingWithColor();
    });
  }

  Future<void> _checkAccess() async {
    // Remove subscription checking - allow access for all authenticated users
    setState(() {
      _hasAccess = true;
      _checkingAccess = false;
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _refreshDoctorName();
      _updateGreeting();
      // Check for updates when app resumes
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    UserProfileService.clearCachedProfile();
    _refreshDoctorName();
    _updateGreeting();
    _checkAccess();

    // Check for updates on app startup (only once)
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  void _refreshDoctorName() {
    setState(() {
      _doctorNameKey = UniqueKey();
    });
  }

  final Map<String, Color> _animeGreetings = {
    'Good day': Colors.blue.shade600,
    'Welcome to a new day': Colors.lightBlue.shade400,
    'What a pleasant day!': Colors.cyan.shade400,
    'Ready for a splendid day': Colors.teal.shade400,
    'time to prove yourself': Colors.green.shade400,
    'Wishing you a great day': Colors.lightGreen.shade500,
    'Hope your day is going well': Colors.lime.shade600,
    'A fresh start to your goal': Colors.amber.shade400,
    'May your day be ECHO-ing': Colors.orange.shade400,
    'What a fine day ': Colors.deepOrange.shade400,

    'Greetings': Colors.deepOrange.shade400,
    'Keep up the good work': Colors.red.shade400,
    'You are doing great today': Colors.pink.shade400,
    'Wishing you a wonderful day': Colors.purple.shade400,
    'A pleasant day': Colors.deepPurple.shade400,
    'A successful day': Colors.indigo.shade400,
    'Making progress': Colors.blue.shade500,
    'The eye of the tiger': const Color.fromARGB(255, 173, 193, 43),

    'A calm day to you': Colors.deepPurple.shade600,
    'Greetings from the heart': Colors.purple.shade600,
    'Time to reflect on today': Colors.pink.shade600,
    'Wishing you a peaceful day': Colors.deepOrange.shade600,
    'Hope you had a productive day': Colors.orange.shade600,
    'A successful day completed': Colors.amber.shade600,
    'New day brings new insights': Colors.yellow.shade700,
    'Closing another valuable day': Colors.lime.shade700,

    'Rasengan': Colors.blue.shade600,
    'Sennin Modo': const Color.fromARGB(255, 255, 145, 0),
    'Sharingan': Colors.red.shade800,
    'Susanoo': Colors.indigo.shade800,
    'Mangekyu Sharingan': Colors.red.shade900,
    'Amaterasu': const Color.fromARGB(255, 47, 0, 0),

    'Kaaameeehaaameeehaaaaaaaaa': Colors.blue.shade600,
    'Super Saiya-jin': Colors.amber.shade600,
    'Final Flash': Colors.yellow.shade700,

    'One For All': Colors.green.shade600,
    'Plus Ultra': Colors.orange.shade700,

    'Gear Secondo': Colors.red.shade500,
    'Goomoo Goomoo no Bazukaaaaa': Colors.orange.shade700,

    'Bankai': const Color.fromARGB(255, 0, 0, 0),

    'Demon Slayer': Colors.teal.shade700,

    'Titan mode': Colors.brown.shade700,

    'Unleash your full potential': Colors.cyan.shade700,
    'Your healing journey begins now': Colors.lightBlue.shade300,
    'Power up for the day ahead': Colors.orange.shade600,
    'Charging to 100 percent': Colors.amber.shade500,
    'Your hidden power awaits': Colors.deepPurple.shade600,
    'Charging through the day with focus': Colors.lightGreen.shade700,
    'Midday power level rising': Colors.orange.shade800,
    'Ultimate technique activated': Colors.deepPurple.shade800,
    'Power at 100 percent': Colors.red.shade500,
    'Awaken your inner dragon': Colors.red.shade700,
    'Your full strength endures': Colors.indigo.shade600,
    'The day\'s final form achieved': Colors.deepPurple.shade900,
    'full power unleashed': Colors.purple.shade700,
    'Mastering the shadows': Colors.blueGrey.shade800,
  };

  Map<String, dynamic> _getRandomGreetingWithColor() {
    final List<String> allGreetings = _animeGreetings.keys.toList();

    final random = DateTime.now().millisecondsSinceEpoch % allGreetings.length;

    final String greeting = allGreetings[random];

    Color color = _animeGreetings[greeting] ?? Theme.of(context).primaryColor;

    return {'greeting': greeting, 'color': color};
  }

  Future<String> _getDoctorName() async {
    UserProfileService.clearCachedProfile();

    final UserProfile userProfile = await UserProfileService.getUserProfile();
    if (userProfile.doctorNameEn.isNotEmpty) {
      return userProfile.doctorNameEn;
    }
    return '';
  }

  Widget _buildProfileBox() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withAlpha(76),
            blurRadius: 8,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ReportCard(
        title: 'Profile',
        description: 'Customize your Report',
        icon: FontAwesomeIcons.user,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const ProfileScreen()),
          ).then((_) {
            // Refresh doctor name when returning from profile screen
            _refreshDoctorName();
          });
        },
        color: Colors.blue,
      ),
    );
  }

  Widget _buildEchoReferenceBox() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.purple.withAlpha(76),
            blurRadius: 8,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ReportCard(
        title: 'Echo Reference',
        description: 'Echo Values',
        icon: FontAwesomeIcons.bookOpen,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const EchoReferenceScreen(),
            ),
          );
        },
        color: Colors.purple,
      ),
    );
  }

  Widget _buildQuickReportBox() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.shade800.withAlpha(76),
            blurRadius: 8,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ReportCard(
        title: 'Quick Report',
        description: 'Basic echo report',
        icon: FontAwesomeIcons.bolt,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const QuickReportScreen()),
          );
        },
        color: Colors.orange.shade800,
      ),
    );
  }

  Widget _buildMyReportsBox() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.secondary.withAlpha(76),
            blurRadius: 8,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ReportCard(
        title: 'My Reports',
        description: 'saved echo reports',
        icon: FontAwesomeIcons.folderOpen,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const MyReportsScreen()),
          );
        },
        color: Theme.of(context).colorScheme.secondary,
      ),
    );
  }

  Future<void> _refreshHomeContent() async {
    try {
      UserProfileService.clearCachedProfile();
      _refreshDoctorName();
      _updateGreeting();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Content refreshed'),
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error refreshing content: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_checkingAccess) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (!_hasAccess) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('ATRIA'),
        elevation: 4,
        backgroundColor: const Color(0xFF1E88E5),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
            onPressed: _refreshHomeContent,
          ),
        ],
      ),
      drawer: const AppDrawer(),
      body: Container(
        color: Colors.grey.shade50,
        child: RefreshIndicator(
          onRefresh: _refreshHomeContent,
          color: Theme.of(context).primaryColor,
          backgroundColor: Colors.white,
          displacement: 40,
          strokeWidth: 3,
          child: ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              Builder(
                builder: (context) {
                  final String greeting = _currentGreeting['greeting'];
                  final Color cardColor = _currentGreeting['color'];

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeInOut,
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(30),
                          blurRadius: 4,
                          spreadRadius: 2,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: cardColor, width: 1.5),
                      ),
                      color: cardColor,
                      margin: EdgeInsets.zero,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: Text(
                            greeting,
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: FontSizes.heading2,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(30),
                      blurRadius: 4,
                      spreadRadius: 2,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  margin: EdgeInsets.zero,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.purple.shade600.withAlpha(20),
                          Colors.purple.shade600.withAlpha(10),
                        ],
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: FutureBuilder<String>(
                          key: _doctorNameKey,
                          future: _getDoctorName(),
                          builder: (context, snapshot) {
                            final doctorName = snapshot.data ?? 'Dr.Waleed';
                            return Text(
                              doctorName,
                              style: Theme.of(
                                context,
                              ).textTheme.headlineSmall?.copyWith(
                                color: Theme.of(context).primaryColor,
                                fontSize: FontSizes.heading2,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.5,
                              ),
                              textAlign: TextAlign.center,
                            );
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(30),
                      blurRadius: 4,
                      spreadRadius: 2,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  margin: EdgeInsets.zero,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.purple.shade600.withAlpha(20),
                          Colors.purple.shade600.withAlpha(10),
                        ],
                      ),
                    ),
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SmartTemplatesScreen(),
                          ),
                        );
                      },
                      borderRadius: BorderRadius.circular(12),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  FaIcon(
                                    FontAwesomeIcons.layerGroup,
                                    color: Colors.purple.shade600,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    'Smart Templates',
                                    style: Theme.of(
                                      context,
                                    ).textTheme.headlineSmall?.copyWith(
                                      color: Colors.purple.shade600,
                                      fontSize: FontSizes.heading2,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 0.5,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 6),
                              Text(
                                'Save and reuse your inputs',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: FontSizes.bodyMedium,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Divider(color: Colors.grey.shade300, thickness: 1.0),
              ),

              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                mainAxisSpacing: 18,
                crossAxisSpacing: 18,
                childAspectRatio: 1,
                children: [
                  _buildProfileBox(),
                  _buildQuickReportBox(),
                  _buildMyReportsBox(),
                  _buildEchoReferenceBox(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
