import 'package:flutter/material.dart';
import '../models/user_profile_model.dart';
import '../services/user_profile_service.dart';

class CustomHeader extends StatefulWidget implements PreferredSizeWidget {
  final String title;

  final bool showBackButton;

  final List<Widget>? actions;

  const CustomHeader({
    super.key,
    required this.title,
    this.showBackButton = true,
    this.actions,
  });

  @override
  State<CustomHeader> createState() => _CustomHeaderState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _CustomHeaderState extends State<CustomHeader> {
  late Future<UserProfile> _profileFuture;

  @override
  void initState() {
    super.initState();
    _profileFuture = UserProfileService.getUserProfile();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserProfile>(
      future: _profileFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting ||
            snapshot.hasError ||
            !snapshot.hasData) {
          return AppBar(
            title: Text(widget.title),
            automaticallyImplyLeading: widget.showBackButton,
            actions: widget.actions,
          );
        }

        final profile = snapshot.data!;

        return AppBar(
          title: Text(widget.title),
          automaticallyImplyLeading: widget.showBackButton,
          actions: widget.actions,

          leading: widget.showBackButton ? null : _buildLeadingWidget(profile),

          flexibleSpace: _buildFlexibleSpace(profile),
        );
      },
    );
  }

  Widget? _buildLeadingWidget(UserProfile profile) {
    if (widget.showBackButton) {
      return null;
    }

    return IconButton(
      icon: const Icon(Icons.menu),
      onPressed: () {
        Scaffold.of(context).openDrawer();
      },
    );
  }

  Widget _buildFlexibleSpace(UserProfile profile) {
    return Container(
      decoration: BoxDecoration(color: Theme.of(context).colorScheme.primary),
      child: SafeArea(
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (!profile.useArabicHeader)
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16.0),
                    child: Text(
                      profile.headerText,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),

              if (profile.useArabicHeader)
                Expanded(
                  flex: 1,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 16.0),
                    child: Text(
                      profile.headerText,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
