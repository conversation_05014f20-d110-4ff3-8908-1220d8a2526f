import 'base_interpretation.dart';

class PulmonaryArteryInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Pulmonary artery assessment was not performed.';
    }

    final echoParams = data['echoParameters'] ?? {};
    final pasp = _parseDouble(echoParams['pasp']);

    final StringBuffer pulmonaryParagraph = StringBuffer();

    if (pasp != null) {
      final String paspInterpretation = _interpretPASP(pasp);
      final String phSeverity = _getPHSeverity(pasp);

      pulmonaryParagraph.write(
        'The pulmonary artery systolic pressure is $pasp mmHg, which is $paspInterpretation. ',
      );

      if (pasp > 30) {
        pulmonaryParagraph.write(
          'This is consistent with $phSeverity pulmonary hypertension.',
        );
      } else {
        pulmonaryParagraph.write(
          'There is no evidence of pulmonary hypertension.',
        );
      }
    } else {
      pulmonaryParagraph.write(
        'Pulmonary artery systolic pressure was not measured.',
      );
    }

    return pulmonaryParagraph.toString();
  }

  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  String _interpretPASP(double pasp) {
    if (pasp <= 30) return 'normal';
    if (pasp <= 45) return 'mildly elevated';
    if (pasp <= 60) return 'moderately elevated';
    return 'severely elevated';
  }

  String _getPHSeverity(double pasp) {
    if (pasp <= 30) return 'no';
    if (pasp <= 45) return 'mild';
    if (pasp <= 60) return 'moderate';
    return 'severe';
  }
}
