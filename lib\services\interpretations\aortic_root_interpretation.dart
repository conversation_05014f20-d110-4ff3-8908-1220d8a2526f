import 'base_interpretation.dart';

class AorticRootInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Aortic root assessment was not performed.';
    }

    final echoParams = data['echoParameters'] ?? {};
    final ao = _parseDouble(echoParams['ao']);

    if (ao != null) {
      final String aoSize = _interpretAoSize(ao);
      return 'The aortic root is $aoSize (Ao: $ao mm).';
    } else {
      return 'Aortic root size was not measured.';
    }
  }

  double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value);
    }
    return null;
  }

  String _interpretAoSize(double ao) {
    if (ao <= 37) return 'Normal';
    if (ao <= 42) return 'Mildly dilated';
    if (ao <= 47) return 'Moderately dilated';
    return 'Severely dilated';
  }
}
