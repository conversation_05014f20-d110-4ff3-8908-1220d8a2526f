import 'base_interpretation.dart';

class TricuspidValveInterpretation implements BaseInterpretation {
  @override
  String generateInterpretation(Map<String, dynamic> data) {
    if (data.isEmpty) {
      return 'Tricuspid valve assessment was not performed.';
    }

    final valveInfo = data['valves'] ?? {};

    final morphologyMap = valveInfo['morphology'] ?? {};
    final tricuspidMorphology =
        morphologyMap['Tricuspid'] as String? ?? 'Normal';

    final morphologyOtherMap = valveInfo['morphologyOther'] ?? {};
    final tricuspidMorphologyOther = morphologyOtherMap['Tricuspid'] as String?;

    final stenosisMap = valveInfo['stenosis'] ?? {};
    final tricuspidStenosis = stenosisMap['Tricuspid'] as String? ?? 'None';

    final regurgitationMap = valveInfo['regurgitation'] ?? {};
    final tricuspidRegurgitation =
        regurgitationMap['Tricuspid'] as String? ?? 'None';

    final prostheticInfo = valveInfo['prosthetic'] ?? {};
    final tricuspidProsthetic = prostheticInfo['tricuspid'] == true;

    final prostheticTypes = valveInfo['prostheticTypes'] ?? {};
    final tricuspidProstheticType = prostheticTypes['tricuspid'] as String?;

    final prostheticFunctions = valveInfo['prostheticFunctions'] ?? {};
    final tricuspidProstheticFunction =
        prostheticFunctions['tricuspid'] as String?;

    final echoParameters = data['echoParameters'] ?? {};
    final tapse = echoParameters['tapse'] as double?;

    final StringBuffer tricuspidParagraph = StringBuffer(
      'The tricuspid valve ',
    );

    if (tricuspidProsthetic) {
      tricuspidParagraph.write('is prosthetic ');

      if (tricuspidProstheticType != null) {
        tricuspidParagraph.write('($tricuspidProstheticType) ');
      }

      if (tricuspidProstheticFunction != null) {
        String lowerCaseFunction = tricuspidProstheticFunction.toLowerCase();

        if (lowerCaseFunction == 'normal') {
          tricuspidParagraph.write('with normal function ');
        } else if (lowerCaseFunction.startsWith('svd')) {
          final svdType = tricuspidProstheticFunction.substring(3);
          final formattedSvdType = _formatCamelCase(svdType);
          tricuspidParagraph.write(
            'with structural valve dysfunction ($formattedSvdType) ',
          );
        } else {
          Map<String, String> dysfunctionDescriptions = {
            'paravalvularleak': 'with paravalvular leak ',
            'ppm': 'with prosthesis-patient mismatch ',
            'pannus': 'with pannus formation ',
            'malposition': 'with malposition ',
            'endocarditis': 'with evidence of endocarditis ',
            'thrombus': 'with thrombus formation ',
          };

          if (dysfunctionDescriptions.containsKey(lowerCaseFunction)) {
            tricuspidParagraph.write(
              dysfunctionDescriptions[lowerCaseFunction]!,
            );
          } else {
            final formattedFunction = _formatCamelCase(
              tricuspidProstheticFunction,
            );
            tricuspidParagraph.write('with $formattedFunction ');
          }
        }
      }
    } else {
      if (tricuspidMorphology.toLowerCase() == 'normal') {
        tricuspidParagraph.write('has normal morphology ');
      } else {
        _addMorphologyDescription(
          tricuspidParagraph,
          tricuspidMorphology,
          tricuspidMorphologyOther,
        );
      }

      if (tricuspidStenosis.toLowerCase() != 'none' ||
          tricuspidRegurgitation.toLowerCase() != 'none') {
        tricuspidParagraph.write('with ');
      } else {
        tricuspidParagraph.write(
          'with no significant stenosis or regurgitation',
        );
      }
    }

    bool needsComma = false;
    if (tricuspidStenosis.toLowerCase() != 'none') {
      Map<String, String> stenosisDescriptions = {
        'mild': 'mild tricuspid stenosis',
        'moderate': 'moderate tricuspid stenosis',
        'severe': 'severe tricuspid stenosis',
        'critical':
            'critical tricuspid stenosis (severe restriction with minimal valve opening)',
      };

      String lowerCaseStenosis = tricuspidStenosis.toLowerCase();
      if (stenosisDescriptions.containsKey(lowerCaseStenosis)) {
        tricuspidParagraph.write(stenosisDescriptions[lowerCaseStenosis]!);
      } else {
        tricuspidParagraph.write('$tricuspidStenosis tricuspid stenosis');
      }

      needsComma = true;
    }

    if (tricuspidRegurgitation.toLowerCase() != 'none') {
      if (needsComma) tricuspidParagraph.write(' and ');

      Map<String, String> regurgitationDescriptions = {
        'trace': 'trace tricuspid regurgitation',
        'mild': 'mild tricuspid regurgitation',
        'moderate': 'moderate tricuspid regurgitation',
        'moderatesevere': 'moderate-to-severe tricuspid regurgitation',
        'severe': 'severe tricuspid regurgitation',
      };

      String lowerCaseRegurgitation = tricuspidRegurgitation.toLowerCase();
      if (regurgitationDescriptions.containsKey(lowerCaseRegurgitation)) {
        tricuspidParagraph.write(
          regurgitationDescriptions[lowerCaseRegurgitation]!,
        );
      } else {
        tricuspidParagraph.write(
          '$tricuspidRegurgitation tricuspid regurgitation',
        );
      }

      needsComma = true;
    }

    if (!tricuspidParagraph.toString().endsWith('.')) {
      tricuspidParagraph.write('.');
    }

    if (tapse != null) {
      tricuspidParagraph.write(' ');

      if (tapse >= 17) {
        tricuspidParagraph.write(
          'TAPSE is ${tapse.toStringAsFixed(1)} mm, indicating normal right ventricular systolic function.',
        );
      } else if (tapse >= 10 && tapse < 17) {
        tricuspidParagraph.write(
          'TAPSE is ${tapse.toStringAsFixed(1)} mm, indicating mildly to moderately reduced right ventricular systolic function.',
        );
      } else {
        tricuspidParagraph.write(
          'TAPSE is ${tapse.toStringAsFixed(1)} mm, indicating severely reduced right ventricular systolic function.',
        );
      }
    }

    return tricuspidParagraph.toString();
  }

  void _addMorphologyDescription(
    StringBuffer paragraph,
    String morphology,
    String? morphologyOther,
  ) {
    String lowerCaseMorphology = morphology.toLowerCase();

    Map<String, String> nativeDescriptions = {
      'bicuspid': 'has bicuspid morphology with fusion of the leaflets ',
      'calcified': 'shows calcification of the leaflets and/or annulus ',
      'myxomatous':
          'demonstrates myxomatous degeneration with leaflet thickening and redundancy ',
      'rheumatic':
          'has rheumatic changes with commissural fusion and leaflet thickening ',
      'thrombus': 'has thrombus formation on the leaflets ',
      'vegetation': 'has vegetations attached to the leaflets ',
    };

    if (lowerCaseMorphology == 'other') {
      if (morphologyOther != null && morphologyOther.isNotEmpty) {
        paragraph.write('has abnormal morphology: $morphologyOther ');
      } else {
        paragraph.write('has abnormal morphology ');
      }
    } else if (nativeDescriptions.containsKey(lowerCaseMorphology)) {
      paragraph.write(nativeDescriptions[lowerCaseMorphology]!);
    } else {
      paragraph.write('has $morphology morphology ');
    }
  }

  String _formatCamelCase(String input) {
    if (input.isEmpty) return input;

    final result = input.replaceAllMapped(
      RegExp(r'([a-z])([A-Z])'),
      (match) => '${match.group(1)} ${match.group(2)}',
    );

    return result.substring(0, 1).toUpperCase() + result.substring(1);
  }
}
